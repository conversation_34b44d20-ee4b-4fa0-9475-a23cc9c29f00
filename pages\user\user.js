// pages/user/user.js
const mapUtils = require('../../utils/mapUtils.js');
const plantsImport = require('./plantsImport.js');
const { apiRequest, authenticatedRequest } = require('../../utils/nodePaymentConfig');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    // 树叶动画相关
    leavesVisible: true,
    leavesZIndex: 10,
    leaves: [],
    // 内容区域动画状态
    contentAnimationActive: false,
    // 用户登录状态
    isLogined: false,
    // 是否为管理员
    isAdmin: false,
    // 用户数据统计
    userStats: {
      collectionCount: 0,
      cartCount: 0,
      discountCount: 0,
      reward: 0,
      level: 0,
      exp: 0
    },
    // 用户唯一标识
    userId: null,
    // 防重复支付标志
    isProcessingPayment: false,
    // 编辑个人资料模态框显示状态
    showEditModal: false,
    // 关于我们模态框显示状态
    showAboutUsModal: false,
    // 充值模态框显示状态
    showRechargeModal: false,
    // 选中的充值金额
    selectedAmount: null,
    // Node.js支付测试相关
    showNodePaymentModal: false,
    selectedNodeAmount: null,
    nodePaymentToken: null,
    // 临时存储编辑中的用户信息
    tempUserInfo: {
      nickName: '',
      avatarUrl: '',
      phoneNumber: '',
    },
    // 经验百分比
    expPercentage: 0,
    // 手机号是否匹配
    phoneNumberMatch: true,
    // 新回价提醒
    hasNewDemandReplies: false,
    // 报价红点提醒
    hasUnviewedQuotes: false,
    // 管理员待处理报价红点提醒
    hasAdminPendingQuotes: false,
    // 树叶动画参数
    leafIdCounter: 0,
    // 是否显示微信昵称输入框
    showNicknameField: false,
    // 页面可见状态
    isPageVisible: true,
    currentTip: {},
    currentWeekday: '',
  },

 //分享
  onShareAppMessage() {
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          title: '工程直采，农户直卖'
        })
      }, 2000)
    })
    return {
      title: '工程直采，农户直卖',
      path: '/pages/home/<USER>',
      imageUrl: 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888/static/LOG_withoutBackground.png',
      promise 
    }
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
   
    // 设置用户是否已登录
    const app = getApp();
    this.setData({
      isLogined: app.globalData.isLogined || false,
      userId: app.globalData.userId || '', // 确保userId始终有值，避免传null给组件
      isAdmin: (app.globalData.userInfo && app.globalData.userInfo.adm === true) || false // 设置管理员状态
    });
    
    // 如果已登录，获取用户信息
    if (this.data.isLogined) {
      this.fetchUserData(getApp().globalData.userId);
      // 检查是否缺少省份信息，如果缺少则尝试获取并更新
      this.checkAndUpdateProvince(getApp().globalData.userId);
    }
    
    // 播放树叶飘落动画
    this.initLeaves();
    
    // 延迟加载内容区域动画
    setTimeout(() => {
      this.setData({
        contentAnimationActive: true
      });
    }, 300);
    
    // 加载当天对应的养护小贴士
    this.loadDailyTip();
   
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 设置页面为可见状态
    this.setData({
      isPageVisible: true,
      leavesVisible: true
    });
    
    // 设置底部tabBar的选中项
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        active: 3  // 我的页面现在是第四个选项，索引为3
      });
    }
    
    // 应用内容动画
    wx.nextTick(() => {
      setTimeout(() => {
        if (this.data.isPageVisible) {
          this.setData({ contentAnimationActive: true });
        }
      }, 50);
    });

    // 确保每次页面显示时都重新加载小贴士
    this.loadDailyTip();

    // 更新需求回价红点状态
    this.updateDemandReplyStatus();

    // 根据用户类型更新不同的红点状态
    if (this.data.isAdmin) {
      // 管理员：更新待处理报价红点状态
      this.updateAdminPendingQuotesStatus();
    } else {
      // 普通用户：更新未查看报价红点状态
      this.updateQuoteRedDotStatus();
    }

    // 延迟再次检查，确保登录状态已更新
    setTimeout(() => {
      if (this.data.isLogined) {
        this.updateDemandReplyStatus();
        if (this.data.isAdmin) {
          this.updateAdminPendingQuotesStatus();
        } else {
          this.updateQuoteRedDotStatus();
        }
      }
    }, 2000);

    // 获取全局登录状态
    const app = getApp();
    
    // 只有在全局已登录状态下才检查登录状态
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      // 使用缓存的头像URL避免闪烁
      let userInfoWithAvatar = {...app.globalData.userInfo};
      if (app.globalData.cachedAvatarUrl) {
        userInfoWithAvatar.avatarUrl = app.globalData.cachedAvatarUrl;
      }

      // 立即使用全局数据更新UI，确保头像实时显示
      this.setData({
        isLogined: true,
        userInfo: userInfoWithAvatar,
        userId: app.globalData.userId,
        isAdmin: userInfoWithAvatar.adm === true, // 设置管理员状态
        userStats: {
          collectionCount: app.globalData.userInfo.collectionCount || 0,
          cartCount: app.globalData.userInfo.cartCount || 0,
          discountCount: app.globalData.userInfo.discountCount || 0,
          reward: app.globalData.userInfo.reward || 0,
          level: app.globalData.userInfo.level || 0,
          exp: app.globalData.userInfo.exp || 0
        }
      });
      
      // 计算并更新经验百分比
      const percentage = this.calcExpPercentage();
      this.setData({ expPercentage: percentage });
      
      // 然后从数据库获取最新的完整数据
      this.fetchUserData(app.globalData.userId);
      
      // 检查是否需要更新省份信息
      this.checkAndUpdateProvince(app.globalData.userId);
      
      // 检查是否需要弹出编辑个人资料窗口
      if (app.globalData.shouldShowUserEdit) {
        // 延迟一小段时间，确保页面加载完毕
        setTimeout(() => {
          this.showEditProfileModal();
          // 重置标记，避免重复弹出
          app.globalData.shouldShowUserEdit = false;
        }, 500);
      }
    } else {
      // 未登录状态，直接显示未登录界面
      this.setData({
        isLogined: false,
        userId: ''  // 使用空字符串而不是null，确保符合String类型要求
      });
      
      // 提示用户点击头像登录
      setTimeout(() => {
        if (this.data.isPageVisible) {
          wx.showToast({
            title: '请点击头像登录',
            icon: 'none',
            duration: 2000
          });
        }
      }, 500);
    }
    
    // 如果叶子数量为0，生成叶子
    if (this.data.leaves.length === 0) {
      setTimeout(() => {
        if (this.data.isPageVisible) {
          this.generateLeaves();
        }
      }, 300);
    }
    
   
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    this.setData({ 
      leavesVisible: false,
      isPageVisible: false
    });
  },

  /**
   * 导航到我的报价页面（复用my-supply页面）
   */
  navigateToMyQuote() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/user/my-supply/my-supply?type=quote&title=我的报价'
    });
  },
  
  /**
   * 导航到报价管理页面（仅管理员可见）
   */
  navigateToQuoteAdmin() {
    if (!this.data.isLogined || !this.data.userInfo.adm) {
      wx.showToast({
        title: '无权访问',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/user/my-supply/my-supply?mode=quote'
    });
  },

  /**
   * 导航到我的收藏页面
   */
  navigateToMyCollection() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/myCollection/myCollection'
    });
  },

  /**
   * 导航到拨号记录页面
   */
  navigateToCallRecords() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/user/call-records/call-records'
    });
  },

  /**
   * 导航到导航记录页面
   */
  navigateToNavigationRecords() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/user/navigation-records/navigation-records'
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清空叶子数组，释放内存
    this.setData({
      isPageVisible: false,
      leavesVisible: false,
      leaves: []
    });
  },

  /**
   * 初始化树叶动画
   */
  initLeaves() {
    // 如果页面不可见，不生成叶子
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 减少初始叶子数量为2-5片
    const leavesCount = Math.floor(Math.random() * 4) + 2;
    const leaves = [];
    
    for (let i = 0; i < leavesCount; i++) {
      leaves.push({
        id: i,
        type: Math.random() > 0.5 ? 1 : 2,
        size: Math.floor(Math.random() * 30) + 40,
        startPos: Math.floor(Math.random() * 100),
        delay: Math.random() * 10,
        duration: Math.floor(Math.random() * 10) + 15
      });
    }
    
    this.setData({ 
      leaves,
      leafIdCounter: leavesCount - 1
    });
  },

  /**
   * 检查用户登录状态
   */
  checkLoginStatus() {
    wx.showLoading({ title: '检查登录状态' });
    
    const app = getApp();
    
    if (app.globalData.isLogined && app.globalData.userInfo && app.globalData.userId) {
      console.log('全局数据中存在用户信息，直接获取完整数据');
      
      // 先使用全局数据快速显示
      this.setData({
        isLogined: true,
        userInfo: app.globalData.userInfo,
        userId: app.globalData.userId,
        isAdmin: app.globalData.userInfo.adm === true, // 设置管理员状态
        userStats: {
          collectionCount: app.globalData.userInfo.collectionCount || 0,
          cartCount: app.globalData.userInfo.cartCount || 0,
          discountCount: app.globalData.userInfo.discountCount || 0,
          reward: app.globalData.userInfo.reward || 0,
          level: app.globalData.userInfo.level || 0,
          exp: app.globalData.userInfo.exp || 0
        }
      });
      
      // 然后从数据库获取最新的完整数据
      this.fetchUserData(app.globalData.userId);
      wx.hideLoading();
    } else {
      console.log('全局数据中不存在用户信息，尝试登录');
      this.getLoginStatus();
    }
  },
  
  /**
   * 获取用户登录状态（通过OpenID）
   */
  getLoginStatus() {
    const db = wx.cloud.database();
    const app = getApp();
    
    // 直接显示未登录状态，不自动登录
    this.setData({
      isLogined: false,
      userId: '' // 将null改为空字符串
    });
    wx.hideLoading();
    
    // 可以在这里添加提示，引导用户点击头像登录
    setTimeout(() => {
      wx.showToast({
        title: '请点击头像登录',
        icon: 'none',
        duration: 2000
      });
    }, 500);

 
  },
  

  
  /**
   * 设置用户数据（内部方法）
   */
  _setUserData(userData, userId, openid) {
    const app = getApp();
    
    // 确保关键字段存在
    userData.level = userData.level || 0;
    userData.exp = userData.exp || 0;
    
    // 如果存在头像FileID，更新全局缓存
    if (userData.avatarFileID) {
      app.cacheAvatarUrl(userData.avatarFileID);
    }
    
    // 使用缓存的头像URL
    if (app.globalData.cachedAvatarUrl) {
      userData.avatarUrl = app.globalData.cachedAvatarUrl;
    }
    
    this.setData({
      isLogined: true,
      userInfo: userData,
      userId: userId,
      isAdmin: userData.adm === true, // 设置管理员状态
      userStats: {
        collectionCount: userData.collectionCount || 0,
        cartCount: userData.cartCount || 0,
        discountCount: userData.discountCount || 0,
        reward: userData.reward || 0,
        level: userData.level || 0,
        exp: userData.exp || 0
      },
      isProcessingLogin: false // 重置登录标志
    });
    
    // 更新经验百分比
    const percentage = this.calcExpPercentage();
    this.setData({ expPercentage: percentage });
    
    // 保存到全局状态和本地存储
    app.saveLoginState(userData, userId, openid);
    wx.hideLoading();
  },

  /**
   * 获取用户数据
   */
   fetchUserData(userId) {
    if (!userId) {
      console.error('获取用户数据失败: 缺少用户ID');
      return;
    }
    
    const db = wx.cloud.database();
    const app = getApp();
    
    db.collection('users').doc(userId).get().then(res => {
      const userData = res.data;
      
      if (userData) {
        //调试
      
        
        // 确保level字段存在
        if (userData.level === undefined || userData.level === null) {
          userData.level = 0;
          db.collection('users').doc(userId).update({
            data: { level: 0 }
          });
        }
        
        // 确保exp字段存在
        if (userData.exp === undefined || userData.exp === null) {
          userData.exp = 0;
          db.collection('users').doc(userId).update({
            data: { exp: 0 }
          });
        }
        
        // 保存头像相关字段
        const avatarFileID = userData.avatarFileID;
        
        // 先设置基本用户数据，但保留当前头像避免闪烁
        let currentAvatarUrl = this.data.userInfo.avatarUrl;
        userData.avatarUrl = currentAvatarUrl || userData.avatarUrl;
        
        this.setData({
          userInfo: userData,
          userStats: {
            collectionCount: userData.collectionCount || 0,
            cartCount: userData.cartCount || 0,
            discountCount: userData.discountCount || 0,
            reward: userData.reward || 0,
            level: userData.level || 0,
            exp: userData.exp || 0
          }
        });
        
        // 如果有新的头像FileID，更新全局缓存
        if (avatarFileID && (!app.globalData.cachedAvatarUrl || avatarFileID !== this.data.userInfo.avatarFileID)) {
          app.cacheAvatarUrl(avatarFileID);
        }
        
        // 更新经验百分比
        const percentage = this.calcExpPercentage();
        this.setData({ expPercentage: percentage });
        
        // 更新全局数据
        app.globalData.userInfo = {
          ...userData,
          avatarUrl: app.globalData.cachedAvatarUrl || userData.avatarUrl
        };
        
        // 更新本地存储
        wx.setStorageSync('userInfo', app.globalData.userInfo);
        
        this.checkLevelUp();
      }
    }).catch(err => {
      console.error('获取用户数据失败:', err);
      // 移除自动清除登录状态的逻辑
      // 网络错误不应该导致用户退出登录
      // 真正的权限问题会在具体的云函数调用中处理

      // 可以显示一个友好的提示，但保持登录状态
      if (err.errCode === -1) {
        console.log('网络连接异常，但保持登录状态');
        // 可以选择显示一个轻量的提示
        // wx.showToast({
        //   title: '网络异常，请稍后重试',
        //   icon: 'none',
        //   duration: 2000
        // });
      }
    });
  },
  /**
   * 获取用户等级信息
   */
  fetchUserLevel() {
    if (!this.data.userId) return;
    
    const db = wx.cloud.database();
    
    db.collection('users').doc(this.data.userId).field({
      level: true,
      exp: true
    }).get().then(res => {
      const userData = res.data;
      
      if (userData) {
        this.setData({
          'userStats.level': userData.level || 1,
          'userStats.exp': userData.exp || 0
        });
      }
    }).catch(err => {
      console.error('获取用户等级失败:', err);
    });
  },




  /**
   * 用户点击头像
   */
  onTapAvatar() {
    if (this.data.isLogined) {
      wx.showToast({
        title: '已登录',
        icon: 'success'
      });
    } else {
      // 防重复登录检查
      if (this.data.isProcessingLogin) {
        console.log('登录正在进行中，忽略重复点击');
        return;
      }

      // 设置登录处理标志
      this.setData({ isProcessingLogin: true });

      // 显示加载提示
      wx.showLoading({ title: '登录中' });
      
      // 检查云环境是否初始化
      if (!wx.cloud) {
        console.error('云环境未初始化');
        this.setData({ isProcessingLogin: false }); // 重置登录标志
        wx.hideLoading();
        wx.showToast({
          title: '云服务不可用，请重启小程序',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
     
      
      // 先通过云函数获取openid
      wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: { type: 'login' },
        success: loginRes => {
          const openid = loginRes.result.openid;
          if (!openid) {
            console.error('获取openid失败:', loginRes);
            wx.hideLoading();
            wx.showToast({
              title: '登录失败，无法获取用户标识',
              icon: 'none',
              duration: 2000
            });
            return;
          }
          
          // 立即保存openid到全局变量和本地存储
          const app = getApp();
          app.globalData.openid = openid;
          wx.setStorageSync('openid', openid);
          
          // 检查数据库中是否已有该用户
          const db = wx.cloud.database();
          db.collection('users').where({ _openid: openid }).get().then(userRes => {
            wx.hideLoading();
            
            if (userRes.data && userRes.data.length > 0) {
              // 用户已存在，获取用户ID和数据
              const userData = userRes.data[0];
              const userId = userData._id;

              // 立即使用现有数据完成登录，提升用户体验
              this._setUserData(userData, userId, openid);

              // 异步更新用户信息和位置，不阻塞登录流程
              this._asyncUpdateUserProfile(userId, openid);
            } else {
              // 用户不存在，需要创建新用户
              wx.getUserProfile({
                desc: '用于完善用户资料',
                success: (profileRes) => {
                  const userInfo = profileRes.userInfo;
                  
                  // 使用mapUtils获取位置和省份信息
                  mapUtils.getCurrentLocationAndReverse({
                    success: (result) => {
                      if (result.status === 0) {
                        const addressComponent = result.result.address_component;
                        const province = addressComponent.province;
                        const latitude = result.result.location.lat;
                        const longitude = result.result.location.lng;
                        
                        // 添加省份信息到userInfo
                        userInfo.province = province;
                        
                        // 创建用户并保存位置信息
                        this.createUserWithLocation(openid, userInfo, longitude, latitude);
                      } else {
                        // 如果逆地址解析失败，尝试使用普通位置
                        wx.getLocation({
                          type: 'wgs84',
                          isHighAccuracy: true,
                          highAccuracyExpireTime: 3000,
                          success: (res) => {
                            const latitude = res.latitude;
                            const longitude = res.longitude;
                            
                            // 创建用户并保存位置信息
                            this.createUserWithLocation(openid, userInfo, longitude, latitude);
                          },
                          fail: (err) => {
                            console.error('获取位置失败:', err);
                            // 即使获取位置失败，也继续创建用户
                            this.createUser(openid, userInfo);
                          }
                        });
                      }
                    },
                    fail: (err) => {
                      console.error('逆地址解析失败:', err);
                      // 即使获取位置失败，也继续创建用户
                      this.createUser(openid, userInfo);
                    }
                  });
                },
                fail: (err) => {
                  console.error('获取用户信息失败:', err);
                  // 创建无头像和昵称的用户
                  this.createUser(openid, null);
                }
              });
            }
          }).catch(err => {
            console.error('查询用户失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none',
              duration: 2000
            });
          });
        },
        fail: err => {
          console.error('云函数login调用失败:', err);
          wx.hideLoading();
          wx.showToast({
            title: '登录失败，请重启小程序后重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }
  },

  /**
   * 异步更新用户资料和位置信息（不阻塞登录流程）
   */
  _asyncUpdateUserProfile(userId, openid) {
    console.log('开始异步更新用户资料和位置信息...');

    // 请求用户授权获取最新信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (profileRes) => {
        const userInfo = profileRes.userInfo;
        console.log('获取用户授权信息成功，开始更新位置...');

        // 异步获取位置和省份信息
        this._asyncUpdateLocationInfo(userId, userInfo, openid);
      },
      fail: (err) => {
        console.log('用户拒绝授权或获取用户信息失败，仅更新位置信息:', err);
        // 即使获取用户信息失败，也尝试更新位置信息
        this._asyncUpdateLocationInfo(userId, null, openid);
      }
    });
  },

  /**
   * 异步更新位置信息
   */
  _asyncUpdateLocationInfo(userId, userInfo, openid) {
    // 使用mapUtils获取位置和省份信息
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const latitude = result.result.location.lat;
          const longitude = result.result.location.lng;

          console.log('位置获取成功:', { province, latitude, longitude });

          if (userInfo) {
            // 有用户信息，更新用户信息和位置
            userInfo.province = province;
            this._silentUpdateUserInfoWithLocation(userId, userInfo, longitude, latitude, openid);
          } else {
            // 仅更新位置信息
            this._silentUpdateLocationOnly(userId, longitude, latitude, province, openid);
          }
        } else {
          console.log('逆地址解析失败，尝试普通定位...');
          // 如果逆地址解析失败，尝试使用普通位置
          this._fallbackLocationUpdate(userId, userInfo, openid);
        }
      },
      fail: (err) => {
        console.log('位置获取失败，尝试普通定位...', err);
        // 位置获取失败，尝试备选方案
        this._fallbackLocationUpdate(userId, userInfo, openid);
      }
    });
  },

  /**
   * 备选位置更新方案
   */
  _fallbackLocationUpdate(userId, userInfo, openid) {
    wx.getLocation({
      type: 'wgs84',
      isHighAccuracy: true,
      highAccuracyExpireTime: 1500, // 缩短超时时间
      success: (res) => {
        const latitude = res.latitude;
        const longitude = res.longitude;
        console.log('备选定位成功:', { latitude, longitude });

        if (userInfo) {
          this._silentUpdateUserInfoWithLocation(userId, userInfo, longitude, latitude, openid);
        } else {
          this._silentUpdateLocationOnly(userId, longitude, latitude, '', openid);
        }
      },
      fail: (err) => {
        console.log('所有位置获取方案都失败，仅更新用户信息:', err);
        // 所有位置获取都失败，如果有用户信息就仅更新用户信息
        if (userInfo) {
          this._silentUpdateUserInfoOnly(userId, userInfo, openid);
        }
      }
    });
  },

  /**
   * 静默更新用户信息和位置（不影响UI）
   */
  _silentUpdateUserInfoWithLocation(userId, userInfo, longitude, latitude, openid) {
    const db = wx.cloud.database();

    const updateData = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      province: userInfo.province || '',
      city: userInfo.city,
      updateTime: db.serverDate(),
      lastLoginTime: db.serverDate(),
      nowPos: db.Geo.Point(longitude, latitude)
    };

    db.collection('users').doc(userId).update({
      data: updateData
    }).then(() => {
      console.log('用户信息和位置静默更新成功');
      // 静默更新全局数据
      this._silentUpdateGlobalData(userId, updateData, openid);
      // 刷新头像URL
      this.refreshAvatarUrl();
    }).catch(err => {
      console.error('用户信息和位置静默更新失败:', err);
    });
  },

  /**
   * 静默更新仅位置信息
   */
  _silentUpdateLocationOnly(userId, longitude, latitude, province, openid) {
    const db = wx.cloud.database();

    const updateData = {
      nowPos: db.Geo.Point(longitude, latitude),
      updateTime: db.serverDate()
    };

    if (province) {
      updateData.province = province;
    }

    db.collection('users').doc(userId).update({
      data: updateData
    }).then(() => {
      console.log('位置信息静默更新成功');
      // 静默更新全局数据中的位置信息
      const app = getApp();
      if (app.globalData.userInfo) {
        app.globalData.userInfo.nowPos = { longitude, latitude };
        if (province) {
          app.globalData.userInfo.province = province;
        }
      }
    }).catch(err => {
      console.error('位置信息静默更新失败:', err);
    });
  },

  /**
   * 静默更新仅用户信息
   */
  _silentUpdateUserInfoOnly(userId, userInfo, openid) {
    const db = wx.cloud.database();

    const updateData = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      city: userInfo.city,
      updateTime: db.serverDate(),
      lastLoginTime: db.serverDate()
    };

    db.collection('users').doc(userId).update({
      data: updateData
    }).then(() => {
      console.log('用户信息静默更新成功');
      // 静默更新全局数据
      this._silentUpdateGlobalData(userId, updateData, openid);
      // 刷新头像URL
      this.refreshAvatarUrl();
    }).catch(err => {
      console.error('用户信息静默更新失败:', err);
    });
  },

  /**
   * 静默更新全局数据
   */
  _silentUpdateGlobalData(userId, updateData, openid) {
    const app = getApp();

    // 更新全局数据
    if (app.globalData.userInfo) {
      Object.assign(app.globalData.userInfo, updateData);
    }

    // 更新本地存储
    try {
      const currentUserInfo = wx.getStorageSync('userInfo') || {};
      const updatedUserInfo = Object.assign(currentUserInfo, updateData);
      wx.setStorageSync('userInfo', updatedUserInfo);
    } catch (err) {
      console.error('更新本地存储失败:', err);
    }
  },

  /**
   * 更新用户位置信息
   */
  updateUserLocation(userId, longitude, latitude) {
    if (!userId) return;
    
    const db = wx.cloud.database();
    db.collection('users').doc(userId).update({
      data: {
        nowPos: db.Geo.Point(longitude, latitude),
        updateTime: db.serverDate()
      }
    }).then(() => {
      //调试
     // console.log('用户位置更新成功');
    }).catch(err => {
    //  console.error('用户位置更新失败:', err);
    });
  },
  
  /**
   * 更新用户信息并包含位置
   */
  updateUserInfoWithLocation(userId, userInfo, longitude, latitude) {
    const db = wx.cloud.database();
    const app = getApp();
    
    // 确保保存完整的用户信息
    const updateData = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      province: userInfo.province || '', // 确保province字段存在
      city: userInfo.city,
      updateTime: db.serverDate(),
      lastLoginTime: db.serverDate(),
      nowPos: db.Geo.Point(longitude, latitude)
    };
    
    db.collection('users').doc(userId).update({
      data: updateData,
      success: res => {
        // 获取完整的用户数据，但保留刚刚更新的昵称和头像
        db.collection('users').doc(userId).get().then(userRes => {
          let userData = userRes.data;
          
          if (userData) {
            // 确保用户数据完整
            userData.level = userData.level || 0;
            userData.exp = userData.exp || 0;
            
            // 确保昵称和头像使用刚刚更新的值
            userData.nickName = userInfo.nickName;
            userData.avatarUrl = userInfo.avatarUrl;
            
            this.setData({
              isLogined: true,
              userInfo: userData,
              userId: userId,
              isAdmin: userData.adm === true, // 设置管理员状态
              userStats: {
                collectionCount: userData.collectionCount || 0,
                cartCount: userData.cartCount || 0,
                discountCount: userData.discountCount || 0,
                reward: userData.reward || 0,
                level: userData.level || 0,
                exp: userData.exp || 0
              }
            });
            
            // 更新经验百分比
            const percentage = this.calcExpPercentage();
            this.setData({ expPercentage: percentage });
            
            // 保存到全局状态和本地存储
            app.globalData.userInfo = userData;
            wx.setStorageSync('userInfo', userData);
            
            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            // 刷新头像URL，确保显示最新头像
            this.refreshAvatarUrl();
          }
        }).catch(fetchErr => {
          console.error('获取更新后的用户数据失败:', fetchErr);
          
          // 即使获取失败，也使用当前的用户信息
          const userData = {
            _id: userId,
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            level: 0,
            exp: 0
          };
          
          this.setData({
            isLogined: true,
            userInfo: userData,
            userId: userId,
            isAdmin: userData.adm === true, // 设置管理员状态
            userStats: {
              collectionCount: 0,
              cartCount: 0,
              discountCount: 0,
              reward: 0,
              level: 0,
              exp: 0
            }
          });
          
          app.globalData.userInfo = userData;
          wx.setStorageSync('userInfo', userData);
          
          wx.hideLoading();
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
        });
      },
      fail: err => {
        console.error('更新用户信息失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 创建带位置信息的新用户
   */
  createUserWithLocation(openid, userInfo, longitude, latitude) {
    if (!openid) {
      wx.showToast({
        title: '注册失败',
        icon: 'error'
      });
      return;
    }
    
    wx.showLoading({
      title: '注册中...',
      mask: true
    });
    
    // 生成随机用户名
    this.generateUniqueNickname().then(uniqueNickname => {
      const db = wx.cloud.database();
      const userData = {
        nickName: uniqueNickname,
        avatarUrl: userInfo ? userInfo.avatarUrl : '',
        gender: userInfo ? userInfo.gender : 0,
        country: userInfo ? userInfo.country : '',
        province: userInfo ? userInfo.province : '',
        city: userInfo ? userInfo.city : '',
        language: userInfo ? userInfo.language : '',
        createTime: new Date(),
        lastLoginTime: new Date(),
        isLogined: true,
        level: 1,
        exp: 0,
        collectionCount: 0,
        cartCount: 0,
        discountCount: 0,
        reward: 50, // 新用户注册奖励50积分
        supplyCount: 0, // 添加供应数量字段，默认为0
        demandCount: 0, // 添加需求数量字段，默认为0
        nowPos: db.Geo.Point(longitude, latitude),
        hasBeenCounted: true // 用户已在generateUniqueNickname函数中被计入统计
      };
      
      db.collection('users').add({
        data: userData
      }).then(res => {
        wx.hideLoading();
        
        const userId = res._id;
        userData._id = userId;
        
        this.setData({
          isLogined: true,
          userInfo: userData,
          userId: userId,
          userStats: {
            collectionCount: 0,
            cartCount: 0,
            discountCount: 0,
            reward: 50, // 新用户注册奖励50积分
            level: 1,
            exp: 0
          }
        });
        
        // 更新全局状态和本地存储
        const app = getApp();
        app.globalData.isLogined = true;
        app.globalData.userInfo = userData;
        app.globalData.userId = userId;
        app.globalData.openid = openid; // 保存openid到全局数据
        
        wx.setStorageSync('isLogined', true);
        wx.setStorageSync('userInfo', userData);
        wx.setStorageSync('userId', userId);
        wx.setStorageSync('openid', openid); // 保存openid到本地存储
        
        wx.showToast({
          title: '注册成功，获得50积分奖励！',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟一下，让用户看到注册成功的提示
            setTimeout(() => {
              // 自动打开编辑个人资料界面
              this.showEditProfileModal();
            }, 2000);
          }
        });
      }).catch(err => {
        console.error('创建用户失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '注册失败，请重试',
          icon: 'none'
        });
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '生成用户名失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 生成唯一的随机用户名
   * @return {Promise<string>} 返回一个Promise，解析为唯一的用户名
   */
  generateUniqueNickname() {
    const generateRandomNickname = () => {
      // 使用时间戳+随机数生成7位数字
      // 取时间戳的后4位
      const timestamp = Date.now().toString().slice(-4);
      // 生成3位随机数字 (000-999)
      const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      // 组合成7位数字
      const sevenDigitNum = timestamp + randomNum;
      return `用户${sevenDigitNum}`;
    };

    const checkNicknameExists = (nickname) => {
      const db = wx.cloud.database();
      return db.collection('users')
        .where({
          nickName: nickname
        })
        .count()
        .then(res => {
          return res.total > 0;
        });
    };

    // 尝试生成唯一用户名，最多尝试5次
    const tryGenerateUnique = (attempts = 0) => {
      if (attempts >= 8) {
        return Promise.reject(new Error('无法生成唯一用户名'));
      }

      const nickname = generateRandomNickname();
      
      return checkNicknameExists(nickname).then(exists => {
        if (exists) {
          // 如果用户名已存在，递归尝试下一个
          return tryGenerateUnique(attempts + 1);
        } else {
          // 找到唯一用户名
          
          // 在这里直接更新用户总数统计，因为生成唯一昵称只在新用户注册时调用
          const db = wx.cloud.database();
          db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').update({
            data: {
              totalUsers: db.command.inc(1)
            }
          }).then(() => {
            console.log('新用户注册：totalUsers统计+1成功');
          }).catch(err => {
            console.error('更新用户总量统计数据失败:', err);
          });
          
          return nickname;
        }
      });
    };

    return tryGenerateUnique();
  },

  /**
   * 获取下一级所需的经验值
   */
  getNextLevelExp(level) {
    level = parseInt(level || 0);
    if (level < 0) level = 0;
    if (level > 6) level = 6;
    
    const levelExpRequirements = {
      0: 50,   // 0级升1级需要50经验
      1: 100,  // 1级升2级需要100经验
      2: 150,  // 2级升3级需要150经验
      3: 200,  // 3级升4级需要200经验
      4: 250,  // 4级升5级需要250经验
      5: 300,  // 5级升6级需要300经验
      6: "max"  // 6级是最高级
    };
    
    return levelExpRequirements[level];
  },
  
  /**
   * 获取当前经验值占总经验的百分比
   */
  getExpPercentage(exp, level) {
    try {
      // 确保转换为数字
      level = parseInt(level || 0);
      exp = parseInt(exp || 0);
      
      // 边界检查
      if (level < 0) level = 0;
      if (level > 6) level = 6;
      
      // 最高级别直接返回100%
      if (level >= 6) {
        return 100;
      }
      
      // 获取当前等级升级所需的总经验
      let maxExp = 0;
      switch (level) {
        case 0: maxExp = 50; break;  // 0级升1级需要50经验
        case 1: maxExp = 100; break; // 1级升2级需要100经验
        case 2: maxExp = 150; break; // 2级升3级需要150经验
        case 3: maxExp = 200; break; // 3级升4级需要200经验
        case 4: maxExp = 250; break; // 4级升5级需要250经验
        case 5: maxExp = 300; break; // 5级升6级需要300经验
      }
      
      // 计算百分比，确保不会除以0
      if (maxExp <= 0) return 0;
      
      // 计算当前经验在总经验中的百分比
      let percentage = (exp / maxExp) * 100;
      
      // 确保百分比在0-100之间
      percentage = Math.min(100, Math.max(0, percentage));
      
      // 四舍五入到整数
      return Math.round(percentage);
    } catch (error) {
      console.error('计算经验百分比出错:', error);
      return 0; // 出错时返回0
    }
  },
  

  /**
   * 检查用户是否可以升级
   */
  checkLevelUp() {
    if (!this.data.isLogined || !this.data.userId) return;
    
    const currentLevel = parseInt(this.data.userInfo.level || 0);
    const currentExp = parseInt(this.data.userInfo.exp || 0);
    
    if (currentLevel >= 6) return;
    
    const expRequired = this.getNextLevelExp(currentLevel);
    
    if (currentExp >= expRequired && expRequired !== "max") {
      const newLevel = currentLevel + 1;
      const remainingExp = newLevel >= 6 ? currentExp : currentExp - expRequired;
      
      const db = wx.cloud.database();
      db.collection('users').doc(this.data.userId).update({
        data: {
          level: newLevel,
          exp: remainingExp
        }
      }).then(res => {
        this.setData({
          'userInfo.level': newLevel,
          'userInfo.exp': remainingExp,
          'userStats.level': newLevel,
          'userStats.exp': remainingExp
        });
        
        // 计算并更新经验百分比
        setTimeout(() => {
          const percentage = this.calcExpPercentage();
          this.setData({ expPercentage: percentage });
        }, 100);
        
        const app = getApp();
        if (app.globalData.userInfo) {
          app.globalData.userInfo.level = newLevel;
          app.globalData.userInfo.exp = remainingExp;
        }
        
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
          userInfo.level = newLevel;
          userInfo.exp = remainingExp;
          wx.setStorageSync('userInfo', userInfo);
        }
        
        wx.showToast({
          title: '恭喜！等级提升至' + newLevel,
          icon: 'success',
          duration: 2000
        });
        
        this.checkLevelUp();
      }).catch(err => {
        console.error('用户升级失败:', err);
      });
    }
  },

  /**
   * 增加经验值
   */
  addExp(e) {
    // 删除此函数内容
  },

  /**
   * 简单计算经验百分比
   */
  calcExpPercentage() {
    // 如果未登录，直接返回0
    if (!this.data.isLogined) {
      return 0;
    }
    
    const level = parseInt(this.data.userInfo.level || 0);
    const exp = parseInt(this.data.userInfo.exp || 0);
    
    if (level >= 6) return 100; // 最高级别返回100%
    
    // 获取当前等级升级所需的总经验
    let maxExp = 50;
    if (level === 0) maxExp = 50;
    else if (level === 1) maxExp = 100;
    else if (level === 2) maxExp = 150;
    else if (level === 3) maxExp = 200;
    else if (level === 4) maxExp = 250;
    else if (level === 5) maxExp = 300;
    
    // 计算百分比
    if (maxExp <= 0) return 0;
    
    const percentage = (exp / maxExp) * 100;
    return Math.min(100, Math.max(0, Math.round(percentage)));
  },

  // 处理树叶动画结束
  onLeafAnimationEnd(e) {
    // 获取结束动画的叶子ID
    const leafId = e.currentTarget.dataset.leafId;
    
    // 从数组中移除该叶子
    const updatedLeaves = this.data.leaves.filter(leaf => leaf.id !== leafId);
    
    this.setData({
      leaves: updatedLeaves
    });
    
    // 如果叶子数量太少且页面可见，生成新的叶子
    // 限制叶子总数，避免过多动画元素
    if (updatedLeaves.length < 3 && this.data.isPageVisible && updatedLeaves.length < 6) {
      this.generateNewLeaf();
    }
  },

  // 生成一片新叶子
  generateNewLeaf() {
    // 如果页面不可见，不生成新叶子
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 限制最大叶子数量为6个
    if (this.data.leaves.length >= 6) {
      return;
    }
    
    const newLeafId = this.data.leafIdCounter + 1;
    
    // 创建一个新叶子对象
    const newLeaf = {
      id: newLeafId,
      type: Math.floor(Math.random() * 2) + 1, // 1或2，对应不同类型叶子
      size: Math.floor(Math.random() * 65) + 55, // 45-110rpx的尺寸
      startPos: Math.random() * 100, // 随机水平位置
      delay: Math.random() * 2, // 0-2秒的随机延迟
      duration: Math.floor(Math.random() * 14) + 17 // 17-31秒的下落时间
    };
    
    // 添加到现有叶子数组
    const updatedLeaves = [...this.data.leaves, newLeaf];
    
    // 更新状态
    this.setData({
      leaves: updatedLeaves,
      leafIdCounter: newLeafId
    });
  },

  // 生成树叶动画相关数据
  generateLeaves() {
    // 如果页面不可见，不生成叶子
    if (!this.data.isPageVisible) {
      return;
    }
    
    // 限制叶子数量为3-5片
    const leavesCount = Math.floor(Math.random() * 3) + 3; 
    const leaves = [];
    let leafIdCounter = 0;
    
    // 确保叶子分布在整个屏幕宽度
    const screenSegments = 5;
    const segmentWidth = 100 / screenSegments;
    
    for (let i = 0; i < leavesCount; i++) {
      // 计算该叶子应该在哪个屏幕区域
      const segment = i % screenSegments;
      // 在该区域内随机位置
      const basePos = segment * segmentWidth;
      const randomOffset = Math.random() * segmentWidth;
      const startPos = basePos + randomOffset;
      
      // 使用自增ID确保唯一性
      leafIdCounter++;
      
      leaves.push({
        id: leafIdCounter,
        type: Math.floor(Math.random() * 2) + 1, // 1或2，对应不同类型叶子
        size: Math.floor(Math.random() * 65) + 45, // 45-110rpx的尺寸
        startPos: startPos, // 分布在整个屏幕宽度的随机水平位置
        delay: Math.random() * 8, // 0-8秒的随机延迟
        duration: Math.floor(Math.random() * 14) + 17 // 17-31秒的下落时间
      });
    }
    
    this.setData({ 
      leaves,
      leavesVisible: true,
      leavesZIndex: 8,
      leafIdCounter
    });
  },


  
 

  /**
   * 创建新用户
   */
  createUser(openid, userInfo) {
    if (!openid) {
      wx.showToast({
        title: '注册失败',
        icon: 'error'
      });
      return;
    }
    
    wx.showLoading({
      title: '注册中...',
      mask: true
    });
    
    // 尝试获取省份信息
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        // 如果获取到省份信息，使用 createUserWithLocation 方法
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const latitude = result.result.location.lat;
          const longitude = result.result.location.lng;
          
          // 如果有userInfo，添加省份信息
          if (userInfo) {
            userInfo.province = province;
          }
          
          // 使用位置信息创建用户
          this.createUserWithLocation(openid, userInfo, longitude, latitude);
        } else {
          // 如果未能获取省份信息，继续创建用户
          this._createUserWithoutLocation(openid, userInfo);
        }
      },
      fail: (err) => {
        console.error('获取位置和省份信息失败:', err);
        // 使用备用方法创建用户
        this._createUserWithoutLocation(openid, userInfo);
      }
    });
  },
  
  /**
   * 在无法获取位置信息时创建用户（内部方法）
   */
  _createUserWithoutLocation(openid, userInfo) {
    // 生成随机用户名
    this.generateUniqueNickname().then(uniqueNickname => {
      const userData = {
        nickName: uniqueNickname,
        avatarUrl: userInfo ? userInfo.avatarUrl : '',
        gender: userInfo ? userInfo.gender : 0,
        country: userInfo ? userInfo.country : '',
        province: userInfo ? userInfo.province : '',
        city: userInfo ? userInfo.city : '',
        language: userInfo ? userInfo.language : '',
        createTime: new Date(),
        lastLoginTime: new Date(),
        isLogined: true,
        level: 1,
        exp: 0,
        collectionCount: 0,
        cartCount: 0,
        discountCount: 0,
        reward: 50, // 新用户注册奖励50积分
        supplyCount: 0, // 添加供应数量字段，默认为0
        demandCount: 0, // 添加需求数量字段，默认为0
        hasBeenCounted: true // 用户已在generateUniqueNickname函数中被计入统计
      };
      
      const db = wx.cloud.database();
      db.collection('users').add({
        data: userData
      }).then(res => {
        wx.hideLoading();
        
        const userId = res._id;
        userData._id = userId;
        
        this.setData({
          isLogined: true,
          userInfo: userData,
          userId: userId,
          userStats: {
            collectionCount: 0,
            cartCount: 0,
            discountCount: 0,
            reward: 50, // 新用户注册奖励50积分
            level: 1,
            exp: 0
          }
        });
        
        // 更新全局状态和本地存储
        const app = getApp();
        app.globalData.isLogined = true;
        app.globalData.userInfo = userData;
        app.globalData.userId = userId;
        app.globalData.openid = openid; // 保存openid到全局数据
        
        wx.setStorageSync('isLogined', true);
        wx.setStorageSync('userInfo', userData);
        wx.setStorageSync('userId', userId);
        wx.setStorageSync('openid', openid); // 保存openid到本地存储
        
        wx.showToast({
          title: '注册成功，获得50积分奖励！',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 延迟一下，让用户看到注册成功的提示
            setTimeout(() => {
              // 自动打开编辑个人资料界面
              this.showEditProfileModal();
            }, 2000);
          }
        });
        
        // 在创建用户后再次尝试获取并更新省份信息
        this.checkAndUpdateProvince(userId);
      }).catch(err => {
        wx.hideLoading();
        
        wx.cloud.callFunction({
          name: 'quickstartFunctions',
          data: {
            type: 'createUser',
            userData: userData
          }
        }).then(result => {
          if (result.result && result.result.success) {
            const newUser = result.result.data;
            const userId = newUser._id;
            
            userData._id = userId;
            
            this.setData({
              isLogined: true,
              userInfo: userData,
              userId: userId,
              userStats: {
                collectionCount: 0,
                cartCount: 0,
                discountCount: 0,
                reward: 50, // 新用户注册奖励50积分
                level: 1,
                exp: 0
              }
            });
            
            // 更新全局状态和本地存储
            const app = getApp();
            app.globalData.isLogined = true;
            app.globalData.userInfo = userData;
            app.globalData.userId = userId;
            app.globalData.openid = openid; // 保存openid到全局数据
            
            wx.setStorageSync('isLogined', true);
            wx.setStorageSync('userInfo', userData);
            wx.setStorageSync('userId', userId);
            wx.setStorageSync('openid', openid); // 保存openid到本地存储
            
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 1500,
              success: () => {
                // 延迟一下，让用户看到登录成功的提示
                setTimeout(() => {
                  // 自动打开编辑个人资料界面
                  this.showEditProfileModal();
                }, 1500);
              }
            });
            
            // 在创建用户后再次尝试获取并更新省份信息
            this.checkAndUpdateProvince(userId);
          } else {
            wx.showToast({
              title: '注册失败，请重试',
              icon: 'none'
            });
          }
        }).catch(cloudErr => {
          let errorMsg = '注册失败';
          if (cloudErr && cloudErr.errMsg) {
            errorMsg = '注册失败: ' + cloudErr.errMsg;
          }
          
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          });
        });
      });
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '生成用户名失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 更新用户信息
   */
  updateUserInfo(userId, userInfo) {
    const db = wx.cloud.database();
    const app = getApp();
    
    // 确保保存完整的用户信息
    const updateData = {
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      province: userInfo.province,
      city: userInfo.city,
      updateTime: db.serverDate(),
      lastLoginTime: db.serverDate()
    };
    
    
    db.collection('users').doc(userId).update({
      data: updateData,
      success: res => {
        // 获取完整的用户数据，但保留刚刚更新的昵称和头像
        db.collection('users').doc(userId).get().then(userRes => {
          let userData = userRes.data;
          
          if (userData) {
            // 确保用户数据完整
            userData.level = userData.level || 0;
            userData.exp = userData.exp || 0;
            
            // 确保昵称和头像使用刚刚更新的值
            userData.nickName = userInfo.nickName;
            userData.avatarUrl = userInfo.avatarUrl;
            
            this.setData({
              isLogined: true,
              userInfo: userData,
              userId: userId,
              userStats: {
                collectionCount: userData.collectionCount || 0,
                cartCount: userData.cartCount || 0,
                discountCount: userData.discountCount || 0,
                reward: userData.reward || 0,
                level: userData.level || 0,
                exp: userData.exp || 0
              }
            });
            
            // 更新经验百分比
            const percentage = this.calcExpPercentage();
            this.setData({ expPercentage: percentage });
            
            // 保存到全局状态和本地存储
            app.globalData.userInfo = userData;
            wx.setStorageSync('userInfo', userData);
            
            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
            
            // 刷新头像URL，确保显示最新头像
            this.refreshAvatarUrl();
          }
        }).catch(fetchErr => {
          console.error('获取更新后的用户数据失败:', fetchErr);
          
          // 即使获取失败，也使用当前的用户信息
          const userData = {
            _id: userId,
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            level: 0,
            exp: 0
          };
          
          this.setData({
            isLogined: true,
            userInfo: userData,
            userId: userId,
            userStats: {
              collectionCount: 0,
              cartCount: 0,
              discountCount: 0,
              reward: 0,
              level: 0,
              exp: 0
            }
          });
          
          app.globalData.userInfo = userData;
          wx.setStorageSync('userInfo', userData);
          
          wx.hideLoading();
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
        });
      },
      fail: err => {
        console.error('更新用户信息失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 退出登录
   */
  logout() {
    const app = getApp();
    const userId = this.data.userId;
    
    if (userId) {
      // 退出前确保用户数据已保存到数据库
      const db = wx.cloud.database();
      db.collection('users').doc(userId).update({
        data: {
          lastLoginTime: db.serverDate(),
          // 确保其他关键数据已保存
          level: this.data.userInfo.level || 0,
          exp: this.data.userInfo.exp || 0
        },
        success: () => {
          //调试
          //console.log('用户数据已保存到数据库');
        },
        fail: (err) => {
          console.error('保存用户数据失败:', err);
        },
        complete: () => {
          // 无论成功失败都清除登录状态
          this._clearLoginState();
        }
      });
    } else {
      // 没有userId直接清除登录状态
      this._clearLoginState();
    }
  },
  
  /**
   * 清除登录状态（内部方法）
   */
  _clearLoginState() {
    const app = getApp();
    app.clearLoginState();
    
    // 重置页面数据为未登录状态
    this.setData({
      isLogined: false,
      userInfo: {},
      userId: '', // 将null改为空字符串
      isAdmin: false, // 重置管理员状态
      userStats: {
        collectionCount: 0,
        cartCount: 0,
        discountCount: 0,
        reward: 0,
        level: 0,
        exp: 0
      },
      expPercentage: 0
    });
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  },

  /**
   * 显示编辑个人资料模态框
   */
  showEditProfileModal() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 获取全局缓存的头像URL
    const app = getApp();
    const avatarUrl = app.globalData.cachedAvatarUrl || this.data.userInfo.avatarUrl;
    const phoneNumber = this.data.userInfo.phoneNumber || '';
    
    this.setData({
      tempUserInfo: {
        nickName: this.data.userInfo.nickName || '',
        avatarUrl: avatarUrl,
        phoneNumber: phoneNumber,
      },
      phoneNumberMatch: phoneNumber ? /^1\d{10}$/.test(phoneNumber) : false,
      phoneNumberIsEmpty: !phoneNumber,
      showEditModal: true,
      avatarUrl: avatarUrl || ''
    });
    
    // 如果是新用户且手机号为空，显示提示
    if (!phoneNumber) {
      setTimeout(() => {
        wx.showToast({
          title: '请设置您的手机号',
          icon: 'none',
          duration: 2500
        });
      }, 500);
    }
  },
  
  /**
   * 微信官方头像选择器的回调函数
   */
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
  
    if (!avatarUrl) {
      console.error('未获取到头像URL');
      wx.showToast({
        title: '获取头像失败',
        icon: 'error'
      });
      return;
    }
    
    // 检查是否已登录和是否有用户ID
    if (!this.data.isLogined || !this.data.userId) {
      console.error('用户未登录或没有用户ID，无法保存头像');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 检查文件是否存在
    const fsm = wx.getFileSystemManager();
    fsm.getFileInfo({
      filePath: avatarUrl,
      success: (fileInfo) => {
        this.processAvatarFile(avatarUrl);
      },
      fail: (err) => {
        console.error('获取头像文件信息失败:', err);
        
        // 尝试使用wx.downloadFile重新获取文件
        wx.downloadFile({
          url: avatarUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              this.processAvatarFile(res.tempFilePath);
            } else {
              wx.showToast({
                title: '头像处理失败',
                icon: 'none'
              });
            }
          },
          fail: (downloadErr) => {
            console.error('下载头像失败:', downloadErr);
            wx.showToast({
              title: '头像处理失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },
  
  /**
   * 处理头像文件
   */
  processAvatarFile(filePath) {
    // 同时更新所有相关的头像URL，立即在界面上显示新头像
    this.setData({
      avatarUrl: filePath,
      'tempUserInfo.avatarUrl': filePath,
    });
    
    // 上传头像到云存储
    wx.showLoading({
      title: '头像处理中',
      mask: true
    });
    
    // 先保存临时头像URL到用户数据，确保界面立即更新
    const db = wx.cloud.database();
    db.collection('users').doc(this.data.userId).update({
      data: {
        avatarUrl: filePath,
        avatarUpdateTime: db.serverDate()
      }
    }).then(() => {
      // 更新全局数据
      const app = getApp();
      if (app.globalData.userInfo) {
        app.globalData.userInfo.avatarUrl = filePath;
        app.globalData.cachedAvatarUrl = filePath;
      }
      
      // 更新本地存储
      const userInfo = wx.getStorageSync('userInfo') || {};
      userInfo.avatarUrl = filePath;
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('cachedAvatarUrl', filePath);
      
      // 显示一个快速的成功提示
      wx.showToast({
        title: '头像已更新',
        icon: 'success',
        duration: 1500
      });
      
      // 然后在后台上传到云存储以获取永久链接
      this.uploadAvatarToCloud(filePath);
    }).catch(err => {
      // 确保在出错时也隐藏加载提示
      wx.hideLoading();
      this.handleAvatarUploadError('保存头像临时URL失败: ' + (err.errMsg || err.message || JSON.stringify(err)));
    });
  },
  
  /**
   * 关闭编辑个人资料模态框
   */
  onEditModalClose() {
    this.setData({
      showEditModal: false,
      tempUserInfo: {
        nickName: '',
        avatarUrl: '',
        phoneNumber: '',
      }
    });
  },
  
  /**
   * 选择头像
   */
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'front',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        wx.showLoading({
          title: '头像处理中',
          mask: true
        });
        this.uploadAvatarToCloud(tempFilePath);
      }
    });
  },
  
  /**
   * 上传头像到云存储
   */
  uploadAvatarToCloud(filePath) {
    if (!this.data.userId) {
      console.error('上传头像失败: 用户ID不存在');
      wx.hideLoading();
      return;
    }
    
    if (!filePath) {
      console.error('上传头像失败: 文件路径为空');
      wx.hideLoading();
      wx.showToast({
        title: '头像文件无效',
        icon: 'none'
      });
      return;
    }
    
    // 先检查文件是否存在
    const fsm = wx.getFileSystemManager();
    fsm.getFileInfo({
      filePath: filePath,
      success: (fileInfo) => {
        this.doUploadAvatarToCloud(filePath);
      },
      fail: (err) => {
        console.error('检查头像文件失败:', err);
        
        // 如果是网络URL，尝试下载后再上传
        if (filePath.startsWith('http')) {
          wx.downloadFile({
            url: filePath,
            success: (res) => {
              if (res.statusCode === 200) {
                this.doUploadAvatarToCloud(res.tempFilePath);
              } else {
                this.handleAvatarUploadError('下载头像失败: ' + res.statusCode);
              }
            },
            fail: (downloadErr) => {
              this.handleAvatarUploadError('下载头像失败: ' + downloadErr.errMsg);
            }
          });
        } else {
          this.handleAvatarUploadError('头像文件不存在: ' + err.errMsg);
        }
      }
    });
  },
  
  /**
   * 执行头像上传到云存储
   */
  doUploadAvatarToCloud(filePath) {
    // 使用固定的文件名格式，确保覆盖旧文件
    const cloudPath = `avatars/user_${this.data.userId}.jpg`;
    
    // 先尝试删除旧文件（如果存在）
    const deleteOldAvatar = () => {
      // 如果存在avatarFileID，先删除它
      if (this.data.userInfo && this.data.userInfo.avatarFileID) {
        return wx.cloud.deleteFile({
          fileList: [this.data.userInfo.avatarFileID]
        }).then(res => {
          return Promise.resolve();
        }).catch(err => {
          console.error('删除旧头像失败:', err);
          return Promise.resolve(); // 即使删除失败也继续上传
        });
      } else {
        return Promise.resolve();
      }
    };
    
    // 先删除旧文件，再上传新文件
    deleteOldAvatar().then(() => {
      // 上传新头像
      return wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: filePath
      });
    }).then(res => {
      const fileID = res.fileID;
      const app = getApp();
      
      // 获取临时URL并更新全局缓存
      return wx.cloud.getTempFileURL({
        fileList: [fileID]
      }).then(urlRes => {
        if (urlRes.fileList && urlRes.fileList.length > 0) {
          const tempFileURL = urlRes.fileList[0].tempFileURL;
          // 添加时间戳查询参数，确保绕过缓存
          const finalUrl = tempFileURL + '?t=' + Date.now();
          
          // 更新全局头像缓存
          app.globalData.cachedAvatarUrl = finalUrl;
          wx.setStorageSync('cachedAvatarUrl', finalUrl);
          
          // 更新数据库中的永久链接和文件ID
          const db = wx.cloud.database();
          return db.collection('users').doc(this.data.userId).update({
            data: {
              avatarUrl: finalUrl,
              avatarFileID: fileID,
              avatarUploadTime: db.serverDate()
            }
          }).then(() => {
            // 更新本地显示
            this.setData({
              avatarUrl: finalUrl,
              'tempUserInfo.avatarUrl': finalUrl,
              'userInfo.avatarUrl': finalUrl,
              'userInfo.avatarFileID': fileID
            });
            
            // 更新全局数据
            if (app.globalData.userInfo) {
              app.globalData.userInfo.avatarUrl = finalUrl;
              app.globalData.userInfo.avatarFileID = fileID;
            }
            
            // 更新本地存储
            const userInfo = wx.getStorageSync('userInfo') || {};
            userInfo.avatarUrl = finalUrl;
            userInfo.avatarFileID = fileID;
            wx.setStorageSync('userInfo', userInfo);
            
            // 隐藏可能的加载提示
            wx.hideLoading();
          }).catch(err => {
            // 确保在数据库更新失败时也隐藏加载提示
            console.error('更新数据库中的头像信息失败:', err);
            wx.hideLoading();
            throw err;
          });
        } else {
          wx.hideLoading();
          throw new Error('获取临时URL失败: 返回结果为空');
        }
      }).catch(err => {
        // 确保在获取临时URL失败时也隐藏加载提示
        wx.hideLoading();
        throw err;
      });
    }).catch(err => {
      this.handleAvatarUploadError('头像上传失败: ' + (err.errMsg || err.message || JSON.stringify(err)));
    });
  },
  
  /**
   * 处理头像上传错误
   */
  handleAvatarUploadError(errorMsg) {
    console.error(errorMsg);
    wx.hideLoading();
    
    // 显示错误提示
    wx.showToast({
      title: '头像上传失败',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 刷新头像URL，确保显示最新头像
   */
  refreshAvatarUrl() {
    // 如果存在avatarFileID，使用它获取最新的临时URL
    if (this.data.userInfo && this.data.userInfo.avatarFileID) {
      wx.cloud.getTempFileURL({
        fileList: [this.data.userInfo.avatarFileID],
        success: res => {
          if (res.fileList && res.fileList.length > 0) {
            const tempFileURL = res.fileList[0].tempFileURL;
            // 添加时间戳查询参数，确保绕过缓存
            const finalUrl = tempFileURL + '?t=' + Date.now();

            // 保留现有的用户信息，只更新头像URL
            const updatedUserInfo = {
              ...this.data.userInfo,
              avatarUrl: finalUrl
            };

            // 保留现有的临时用户信息，只更新头像URL
            const updatedTempUserInfo = {
              ...this.data.tempUserInfo,
              avatarUrl: finalUrl
            };

            this.setData({
              userInfo: updatedUserInfo,
              tempUserInfo: updatedTempUserInfo
            });

            // 更新全局数据，保留其他字段
            const app = getApp();
            if (app.globalData.userInfo) {
              app.globalData.userInfo = {
                ...app.globalData.userInfo,
                avatarUrl: finalUrl
              };
            }

            // 更新本地存储，保留其他字段
            const userInfo = wx.getStorageSync('userInfo');
            if (userInfo) {
              userInfo.avatarUrl = finalUrl;
              wx.setStorageSync('userInfo', userInfo);
            }
          }
        }
      });
    }
  },


  
  
  /**
   * 昵称输入变化处理
   */
  onNicknameChange(e) {
    const nickName = e.detail.value;
    
    this.setData({
      'tempUserInfo.nickName': nickName
    });
    
    if (nickName !== '' && nickName.trim() === '') {
      wx.showToast({
        title: '昵称不能全为空格',
        icon: 'none',
        duration: 1000
      });
    }
  },
  
  /**
   * 昵称清空处理
   */
  onNicknameClear() {
    this.setData({
      'tempUserInfo.nickName': ''
    });
    
    wx.showToast({
      title: '请输入有效昵称',
      icon: 'none',
      duration: 1500
    });
  },
  
  // 移除了onPhoneNumberChange和onPhoneNumberClear函数，因为文本框已设置为不可手动输入
  
  /**
   * 检查手机号是否有效
   */
  checkPhoneNumberMatch() {
    const { phoneNumber } = this.data.tempUserInfo;
    
    // 检查手机号是否为空
    if (!phoneNumber || phoneNumber.trim() === '') {
      this.setData({
        phoneNumberMatch: false,
        phoneNumberIsEmpty: true
      });
      return;
    }
    
    // 检查手机号格式
    const isValidFormat = /^1\d{10}$/.test(phoneNumber);
    
    this.setData({
      phoneNumberMatch: isValidFormat,
      phoneNumberIsEmpty: false
    });
  },
  
  /**
   * 保存个人资料修改
   */
  saveProfileChanges() {
    // 验证昵称
    if (this.data.tempUserInfo.nickName === undefined) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.tempUserInfo.nickName.trim() === '') {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 验证手机号
    const { phoneNumber } = this.data.tempUserInfo;
    
    // 必须输入手机号
    if (!phoneNumber || phoneNumber.trim() === '') {
      wx.showToast({
        title: '请设置手机号',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 检查手机号格式
    if (!/^1\d{10}$/.test(phoneNumber)) {
      wx.showToast({
        title: '请输入有效的11位手机号',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中',
      mask: true
    });
    
    const db = wx.cloud.database();
    const app = getApp();
    
    const updateData = {
      nickName: this.data.tempUserInfo.nickName.trim()
    };
    
    // 如果头像有更新，添加到更新数据中
    if (this.data.tempUserInfo.avatarUrl && this.data.tempUserInfo.avatarUrl !== this.data.userInfo.avatarUrl) {
      updateData.avatarUrl = this.data.tempUserInfo.avatarUrl;
    }
    
    // 如果手机号有更新，添加到更新数据中
    if (phoneNumber && phoneNumber !== this.data.userInfo.phoneNumber) {
      updateData.phoneNumber = phoneNumber;
    }
    
    updateData.updateTime = db.serverDate();
    
    db.collection('users').doc(this.data.userId).update({
      data: updateData,
      success: res => {
        const updatedUserInfo = {
          ...this.data.userInfo,
          ...updateData
        };
        
        this.setData({
          userInfo: updatedUserInfo,
          showEditModal: false,
          tempUserInfo: {
            nickName: '',
            avatarUrl: '',
            phoneNumber: '',
          }
        });
        
        // 确保全局状态和本地存储都更新
        app.globalData.userInfo = updatedUserInfo;
        wx.setStorageSync('userInfo', updatedUserInfo);
        
        // 确保全局状态中的头像URL与当前一致
        if (updateData.avatarUrl) {
          app.globalData.userInfo.avatarUrl = updateData.avatarUrl;
          
          // 强制刷新头像缓存
          wx.cloud.getTempFileURL({
            fileList: [updateData.avatarUrl],
            success: urlRes => {
              const avatarUrl = urlRes.fileList[0].tempFileURL;
              app.globalData.userInfo.avatarUrl = avatarUrl;
              
              // 更新本地存储
              const userInfo = wx.getStorageSync('userInfo');
              if (userInfo) {
                userInfo.avatarUrl = avatarUrl;
                wx.setStorageSync('userInfo', userInfo);
              }
            }
          });
        }
        
        wx.hideLoading();
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 刷新头像URL，确保显示最新头像
        this.refreshAvatarUrl();
      },
      fail: err => {
        console.error('更新用户资料失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 昵称输入处理
   */
  onInputNickname(e) {
    this.setData({
      'tempUserInfo.nickName': e.detail.value
    });
  },

  /**
   * 使用微信昵称
   */
  useWechatNickname() {
    const that = this;
    wx.getUserProfile({
      desc: '用于完善个人资料', // 声明获取用户个人信息后的用途
      success: function(res) {
        const userInfo = res.userInfo;
        that.setData({
          'tempUserInfo.nickName': userInfo.nickName
        });
        wx.showToast({
          title: '已使用微信昵称',
          icon: 'success',
          duration: 1500
        });
      },
      fail: function(err) {
        console.log('获取用户信息失败', err);
        wx.showModal({
          title: '提示',
          content: '获取微信昵称失败，请手动输入或重试',
          showCancel: false
        });
      }
    });
  },

  /**
   * 根据用户等级获取对应的CSS类名
   * @param {number} level 用户等级
   * @return {string} 对应的CSS类名
   */
  getLevelClass(level) {
    const levelMap = {
      0: 'level-0',
      1: 'level-1',
      2: 'level-2',
      3: 'level-3',
      4: 'level-4',
      5: 'level-5',
      6: 'level-6'
    };
    return levelMap[level] || 'level-0';
  },

  /**
   * 加载当天对应的养护小贴士
   */
  loadDailyTip: function() {
   
    // 养护小贴士列表
    const tips = [
      {
        title: "夏季花卉浇水最佳时间",
        desc: "清晨或傍晚浇水，避免烈日暴晒时",
        icon: "help-circle"
      },
      {
        title: "绿植叶面清洁小技巧",
        desc: "用湿软布轻擦叶面，去除灰尘提高光合作用",
        icon: "help-circle"
      },
      {
        title: "花卉浇水的黄金法则",
        desc: "宁干勿湿，浇则浇透，不要积水",
        icon: "help-circle"
      },
      {
        title: "室内绿植最佳位置",
        desc: "光照充足但避免强光直射的位置最佳",
        icon: "help-circle"
      },
      {
        title: "植物黄叶的处理方法",
        desc: "及时剪除黄叶，预防病虫害蔓延",
        icon: "help-circle"
      },
      {
        title: "盆栽植物换盆时机",
        desc: "春季是换盆的最佳时间，根系生长活跃",
        icon: "help-circle"
      },
      {
        title: "施肥的正确方法",
        desc: "薄肥勤施，避免肥料直接接触根系",
        icon: "help-circle"
      }
    ];
    
    // 获取当前是星期几（0-6，0表示星期日）
    const today = new Date().getDay();
    
    
    // 根据星期几选择对应的小贴士
    let tipIndex = 0;
    if (today === 0) {
      tipIndex = 6; // 星期日显示第7条
    } else {
      tipIndex = today - 1; // 其他日期对应显示
    }
    
    // 确保索引在有效范围内
    if (tipIndex < 0 || tipIndex >= tips.length) {
      console.error('小贴士索引超出范围:', tipIndex);
      tipIndex = 0; // 默认使用第一条
    }
    
   
    
    // 立即更新数据，避免异步问题
    const selectedTip = tips[tipIndex];
    const weekdayName = this.getWeekdayName(today);
    

    
    // 更新数据
    this.setData({
      currentTip: selectedTip,
      currentWeekday: weekdayName
    }, () => {
      // 在回调中再次确认数据已设置
     
    });
  },
  
  /**
   * 获取星期几的中文名称
   */
  getWeekdayName: function(day) {
    const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    return weekdays[day];
  },

  /**
   * 获取手机号
   * 当用户点击获取手机号按钮后，微信会返回加密的手机号信息
   */
  getPhoneNumber(e) {
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      wx.showToast({
        title: '授权失败，请重试',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '获取手机号中',
      mask: true
    });
    
    // 检查用户是否已登录
    if (!this.data.isLogined || !this.data.userId) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 获取手机号需要通过云函数调用
    const code = e.detail.code;
    
    // 调用云函数获取手机号
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      data: {
        type: 'getPhoneNumber',
        code: code
      },
      success: res => {
        if (res.result && res.result.success && res.result.phoneInfo) {
          // 获取到手机号码
          const phoneNumber = res.result.phoneInfo.phoneNumber || res.result.phoneInfo.purePhoneNumber;
          
          if (phoneNumber) {
            // 更新用户资料中的手机号
            const db = wx.cloud.database();
            db.collection('users').doc(this.data.userId).update({
              data: {
                phoneNumber: phoneNumber
              }
            }).then(() => {
              wx.hideLoading();
        
              // 更新本地数据
              const userInfo = this.data.userInfo;
              userInfo.phoneNumber = phoneNumber;
          
              this.setData({
                'userInfo.phoneNumber': phoneNumber,
                'tempUserInfo.phoneNumber': phoneNumber,
                // 更新手机号验证状态，启用保存按钮
                phoneNumberMatch: true,
                phoneNumberIsEmpty: false
              });
              
              // 更新全局数据
              const app = getApp();
              if (app.globalData.userInfo) {
                app.globalData.userInfo.phoneNumber = phoneNumber;
                
                // 更新存储的用户信息
                wx.setStorageSync('userInfo', app.globalData.userInfo);
              }
          
              wx.showToast({
                title: '手机号获取成功',
                icon: 'success'
              });
            }).catch(err => {
              wx.hideLoading();
              console.error('更新用户手机号失败:', err);
              
              wx.showToast({
                title: '手机号保存失败，请重试',
                icon: 'none'
              });
            });
          } else {
            wx.hideLoading();
            wx.showToast({
              title: '获取手机号失败，信息不完整',
              icon: 'none'
            });
          }
        } else {
          wx.hideLoading();
          console.error('获取手机号失败:', res);
          
          let errorMsg = '获取手机号失败';
          if (res.result && res.result.errMsg) {
            errorMsg = res.result.errMsg;
          }
          
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('调用获取手机号云函数失败:', err);
        
        wx.showToast({
          title: '获取手机号失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 导入植物数据到云数据库
   */
  importPlantsData() {
    // 显示确认对话框
    wx.showModal({
      title: '导入植物数据',
      content: '确定要导入植物数据到云数据库吗？这可能需要一些时间。',
      confirmText: '确定导入',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          // 检查数据库中是否已有植物数据
          const checkResult = await plantsImport.checkPlantsData();
          
          if (checkResult.success && checkResult.count > 0) {
            // 如果已有数据，再次确认是否继续导入
            wx.showModal({
              title: '数据已存在',
              content: `数据库中已有${checkResult.count}条植物数据，是否继续导入？`,
              confirmText: '继续导入',
              cancelText: '取消',
              success: (res2) => {
                if (res2.confirm) {
                  // 执行导入操作
                  this.doImportPlantsData();
                }
              }
            });
          } else {
            // 直接执行导入操作
            this.doImportPlantsData();
          }
        }
      }
    });
  },
  
  /**
   * 执行植物数据导入操作
   */
  async doImportPlantsData() {
    try {
      const result = await plantsImport.importPlantsData();
      
      if (result.success) {
        wx.showToast({
          title: result.message,
          icon: 'success',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('导入植物数据失败:', error);
      wx.showToast({
        title: '导入失败，请检查网络',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 处理签到成功事件
   * @param {Object} e - 事件对象
   */
  onSignInSuccess(e) {
    const { reward } = e.detail;
    
    // 更新用户积分显示
    if (this.data.userInfo && this.data.userInfo.reward !== undefined) {
      const updatedUserInfo = { ...this.data.userInfo };
      updatedUserInfo.reward = (parseFloat(updatedUserInfo.reward) + parseFloat(reward)).toFixed(1);
      
      this.setData({
        userInfo: updatedUserInfo
      });
      
      // 更新全局数据
      const app = getApp();
      if (app.globalData.userInfo) {
        app.globalData.userInfo.reward = updatedUserInfo.reward;
      }
      
      // 显示签到成功提示
      wx.showToast({
        title: `签到成功，获得${reward}积分`,
        icon: 'success',
        duration: 2000
      });
    }
  },
  
  /**
   * 客服电话拨打
   */
  callCustomerService() {
    wx.showModal({
      title: '联系客服',
      content: '是否拨打客服电话：18384104768？',
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '18384104768',
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败，请手动拨号',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  

  
  /**
   * 检查并更新用户的省份信息
   */
  checkAndUpdateProvince(userId) {
    if (!userId) return;
    
    const db = wx.cloud.database();
    db.collection('users').doc(userId).get().then(res => {
      const userData = res.data;
      if (!userData || !userData.province) {
        console.log('用户缺少省份信息，尝试更新...');
        // 尝试获取省份信息并更新
        mapUtils.getCurrentLocationAndReverse({
          success: (result) => {
            if (result.status === 0) {
              const addressComponent = result.result.address_component;
              const province = addressComponent.province;
              const latitude = result.result.location.lat;
              const longitude = result.result.location.lng;
              
              // 更新用户的省份信息
              db.collection('users').doc(userId).update({
                data: {
                  province: province,
                  nowPos: db.Geo.Point(longitude, latitude),
                  updateTime: db.serverDate()
                }
              }).then(() => {
                console.log('用户省份和位置信息更新成功');
                // 重新获取用户数据以刷新界面
                this.fetchUserData(userId);
              }).catch(err => {
                console.error('用户省份和位置信息更新失败:', err);
              });
            }
          },
          fail: (err) => {
            console.error('获取位置和省份信息失败:', err);
          }
        });
      }
    }).catch(err => {
      console.error('获取用户数据失败:', err);
    });
  },

  /**
   * 导航到我的供应页面
   */
  navigateToMySupply: function() {
    wx.navigateTo({
      url: '/pages/user/my-supply/my-supply'
    });
  },

  /**
   * 导航到我的求购页面
   */
  navigateToMyDemand: function() {
    wx.navigateTo({
      url: '/pages/user/my-demand/my-demand'
    });
  },

  /**
   * 显示关于我们弹窗
   */
  showAboutUsModal() {
    this.setData({
      showAboutUsModal: true
    });
  },
  
  /**
   * 关闭关于我们弹窗
   */
  onAboutUsModalClose() {
    this.setData({
      showAboutUsModal: false
    });
  },



  /**
   * 页面分享配置
   */
  onShareAppMessage() {
    return {
      title: '成都苗木中心 - 绿意盎然·美好生活',
      path: '/pages/home/<USER>',
      imageUrl: 'https://6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888.tcb.qcloud.la/static/LOG_withoutBackground.png?sign=ad4a944b6fdd50a955a6da3e49379f5f&t=1751186170'
    };
  },

  /**
   * 显示充值弹窗
   */
  showRechargeModal() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showRechargeModal: true,
      selectedAmount: null
    });
  },

  /**
   * 充值弹窗显示状态变化
   */
  onRechargeModalChange(e) {
    this.setData({
      showRechargeModal: e.detail.visible
    });
  },

  /**
   * 隐藏充值弹窗
   */
  hideRechargeModal() {
    this.setData({
      showRechargeModal: false,
      selectedAmount: null
    });
  },

  /**
   * 选择充值金额
   */
  selectAmount(e) {
    const amount = parseFloat(e.currentTarget.dataset.amount);
    this.setData({
      selectedAmount: amount
    });
  },

  /**
   * 确认充值
   */
  confirmRecharge() {
    if (!this.data.selectedAmount) {
      wx.showToast({
        title: '请选择充值金额',
        icon: 'none'
      });
      return;
    }

    const amount = this.data.selectedAmount;
    // 特殊处理一分钱充值：0.01元=1积分，其他按1元=1积分
    const points = amount === 0.01 ? 1 : Math.floor(amount);

    wx.showModal({
      title: '确认充值',
      content: `确认充值${amount}元，获得${points}积分？`,
      success: (res) => {
        if (res.confirm) {
          this.processRecharge(amount, points);
        }
      }
    });
  },

  /**
   * 处理充值逻辑
   */
  async processRecharge(amount, points) {
    // 检查登录状态
    if (!this.data.isLogined || !this.data.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 防重复支付检查
    if (this.data.isProcessingPayment) {
      wx.showToast({
        title: '支付处理中，请稍候',
        icon: 'none'
      });
      return;
    }

    // 设置支付处理状态
    this.setData({ isProcessingPayment: true });

    wx.showLoading({
      title: '发起支付...'
    });

    try {
      // 调试信息
      console.log('当前登录状态:', this.data.isLogined);
      console.log('当前用户ID:', this.data.userId);
      console.log('全局登录状态:', getApp().globalData.isLogined);
      console.log('全局用户信息:', getApp().globalData.userInfo);

      // 1. 调用云函数创建支付订单
      const createResult = await wx.cloud.callFunction({
        name: 'createPayment',
        data: {
          amount: amount,
          points: points,
          description: `积分充值${points}积分`
        }
      });

      if (!createResult.result.success) {
        throw new Error(createResult.result.error || '创建支付订单失败');
      }

      wx.hideLoading();

      // 2. 调用微信支付
      const paymentResult = await wx.requestPayment({
        timeStamp: createResult.result.timeStamp,
        nonceStr: createResult.result.nonceStr,
        package: createResult.result.package,
        signType: createResult.result.signType,
        paySign: createResult.result.paySign
      });

      // 3. 支付成功，验证积分到账
      wx.showLoading({
        title: '确认积分到账...'
      });

      // 记录支付前的积分
      const beforePoints = this.data.userInfo.reward || 0;

      // 等待回调处理，并验证积分变化
      const success = await this.verifyPaymentResult(
        createResult.result.out_trade_no,
        beforePoints,
        points,
        10000 // 10秒超时
      );

      wx.hideLoading();

      if (success) {
        wx.showToast({
          title: '充值成功',
          icon: 'success'
        });
        this.hideRechargeModal();
      } else {
        wx.showModal({
          title: '支付确认',
          content: '支付已完成，但积分到账可能有延迟，请稍后查看或联系客服',
          showCancel: false,
          confirmText: '知道了'
        });
      }
      


      // 重置支付处理状态
      this.setData({ isProcessingPayment: false });

    } catch (error) {
      wx.hideLoading();

      // 重置支付处理状态
      this.setData({ isProcessingPayment: false });

      if (error.errMsg === 'requestPayment:fail cancel') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: error.message || '支付失败，请重试',
          icon: 'none'
        });
        console.error('充值失败:', error);
      }
    }
  },

  /**
   * 验证支付结果和积分到账
   * @param {string} orderNo 订单号
   * @param {number} beforePoints 支付前积分
   * @param {number} expectedPoints 预期增加的积分
   * @param {number} timeout 超时时间（毫秒）
   * @returns {boolean} 是否验证成功
   */
  async verifyPaymentResult(orderNo, beforePoints, expectedPoints, timeout = 10000) {
    const startTime = Date.now();
    const interval = 1000; // 每1秒检查一次

    console.log(`验证支付结果: 订单${orderNo}, 支付前积分${beforePoints}, 预期增加${expectedPoints}`);

    while (Date.now() - startTime < timeout) {
      try {
        // 刷新用户数据
        await this.fetchUserData(this.data.userId);

        const currentPoints = this.data.userInfo.reward || 0;
        const actualIncrease = currentPoints - beforePoints;

        console.log(`当前积分: ${currentPoints}, 实际增加: ${actualIncrease}, 预期增加: ${expectedPoints}`);

        // 检查积分是否正确增加
        if (actualIncrease >= expectedPoints) {
          console.log('积分到账验证成功');
          return true;
        }

        // 等待下次检查
        await new Promise(resolve => setTimeout(resolve, interval));

      } catch (error) {
        console.error('验证支付结果时出错:', error);
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    console.log('积分到账验证超时');
    return false;
  },

  /**
   * 更新用户积分
   */
  async updateUserPoints(points) {
    const db = wx.cloud.database();
    const userId = this.data.userId;

    try {
      // 更新数据库中的用户积分
      await db.collection('users').doc(userId).update({
        data: {
          reward: db.command.inc(points) // 增加积分
        }
      });

      // 更新本地数据
      const newReward = (this.data.userInfo.reward || 0) + points;
      this.setData({
        'userInfo.reward': newReward,
        'userStats.reward': newReward
      });

      // 更新全局数据
      const app = getApp();
      if (app.globalData.userInfo) {
        app.globalData.userInfo.reward = newReward;
      }

    } catch (error) {
      console.error('更新积分失败:', error);
      throw error;
    }
  },

  /**
   * 更新需求回价红点状态（聚合查询优化版）
   * 解决分页查询导致的红点丢失问题
   */
  updateDemandReplyStatus: function() {
    // 只有登录用户才需要检查
    if (!this.data.isLogined) {
      this.setData({ hasNewDemandReplies: false });
      return;
    }

    // 调用优化后的聚合查询云函数
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      data: {
        type: 'getTotalNewRepliesCount'
      },
      success: res => {
        const result = res.result || {};
       

        if (result.code === 0) {
          // 使用聚合查询的结果更新红点状态
          this.setData({
            hasNewDemandReplies: result.hasNewReplies || false,
            totalNewReplies: result.totalNewReplies || 0,
            hasNewReplyCount: result.hasNewReplyCount || 0
          });

         
        } else {
         
          this.setData({ hasNewDemandReplies: false });
        }
      },
      fail: err => {
        console.error('调用红点统计云函数失败:', err);
        // 失败时不显示红点
        this.setData({ hasNewDemandReplies: false });
      }
    });
  },

  /**
   * 更新报价红点状态（聚合查询优化版）
   * 仅针对普通用户（adm == false）
   */
  updateQuoteRedDotStatus: function() {
    // 只有登录的普通用户才需要检查
    if (!this.data.isLogined || this.data.isAdmin) {
      this.setData({ hasUnviewedQuotes: false });
      return;
    }

    // 调用优化后的聚合查询云函数
    wx.cloud.callFunction({
      name: 'getTotalUnviewedQuotesCount',
      data: {},
      success: res => {
        const result = res.result || {};

        if (result.code === 0) {
          // 使用聚合查询的结果更新红点状态
          this.setData({
            hasUnviewedQuotes: result.hasUnviewedQuotes || false
          });
        } else {
          this.setData({ hasUnviewedQuotes: false });
        }
      },
      fail: err => {
        console.error('调用报价红点统计云函数失败:', err);
        // 失败时不显示红点
        this.setData({ hasUnviewedQuotes: false });
      }
    });
  },

  /**
   * 更新管理员待处理报价红点状态
   * 仅针对管理员用户（adm == true）
   */
  updateAdminPendingQuotesStatus: function() {
    // 只有登录的管理员才需要检查
    if (!this.data.isLogined || !this.data.isAdmin) {
      this.setData({ hasAdminPendingQuotes: false });
      return;
    }

    // 调用管理员专用的待处理报价统计云函数
    wx.cloud.callFunction({
      name: 'getAdminPendingQuotesCount',
      data: {},
      success: res => {
        const result = res.result || {};

        if (result.code === 0) {
          // 使用聚合查询的结果更新管理员红点状态
          this.setData({
            hasAdminPendingQuotes: result.hasPendingQuotes || false
          });
        } else {
          this.setData({ hasAdminPendingQuotes: false });
        }
      },
      fail: err => {
        console.error('调用管理员待处理报价统计云函数失败:', err);
        // 失败时不显示红点
        this.setData({ hasAdminPendingQuotes: false });
      }
    });
  },

  // ==================== Node.js支付测试相关方法 ====================

  /**
   * 显示Node.js支付测试弹窗
   */
  showNodePaymentModal() {
    if (!this.data.isLogined) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showNodePaymentModal: true,
      selectedNodeAmount: null
    });
  },

  /**
   * Node.js支付弹窗显示状态变化
   */
  onNodePaymentModalChange(e) {
    this.setData({
      showNodePaymentModal: e.detail.visible
    });
  },

  /**
   * 隐藏Node.js支付弹窗
   */
  hideNodePaymentModal() {
    this.setData({
      showNodePaymentModal: false,
      selectedNodeAmount: null
    });
  },

  /**
   * 选择Node.js支付金额
   */
  selectNodeAmount(e) {
    const amount = parseFloat(e.currentTarget.dataset.amount);
    this.setData({
      selectedNodeAmount: amount
    });
  },

  /**
   * 确认Node.js支付
   */
  confirmNodePayment() {
    if (!this.data.selectedNodeAmount) {
      wx.showToast({
        title: '请选择充值金额',
        icon: 'none'
      });
      return;
    }

    const amount = this.data.selectedNodeAmount;
    // 特殊处理一分钱充值：0.01元=1积分，其他按1元=1积分
    const points = amount === 0.01 ? 1 : Math.floor(amount);

    wx.showModal({
      title: '确认充值',
      content: `确认充值${amount}元，获得${points}积分？`,
      success: (res) => {
        if (res.confirm) {
          this.processNodePayment(amount, points);
        }
      }
    });
  },

  /**
   * 处理Node.js支付逻辑
   */
  async processNodePayment(amount, points) {
    // 检查登录状态
    if (!this.data.isLogined || !this.data.userId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 防重复支付检查
    if (this.data.isProcessingPayment) {
      wx.showToast({
        title: '支付处理中，请稍候',
        icon: 'none'
      });
      return;
    }

    // 设置支付处理状态
    this.setData({ isProcessingPayment: true });

    try {
      // 1. 先进行微信登录获取token
      wx.showLoading({
        title: '获取授权...'
      });

      const token = await this.getNodePaymentToken();
      if (!token) {
        throw new Error('获取授权失败');
      }

      // 2. 创建支付订单
      wx.showLoading({
        title: '创建订单...'
      });

      const orderResult = await this.createNodePaymentOrder(token, amount, points);
      if (!orderResult.success) {
        throw new Error(orderResult.message || '创建订单失败');
      }

      wx.hideLoading();

      // 3. 调用微信支付
      const paymentResult = await wx.requestPayment({
        timeStamp: orderResult.data.timeStamp,
        nonceStr: orderResult.data.nonceStr,
        package: orderResult.data.package,
        signType: orderResult.data.signType,
        paySign: orderResult.data.paySign
      });

      // 4. 支付成功，验证结果
      wx.showLoading({
        title: '验证支付结果...'
      });

      const verifyResult = await this.verifyNodePaymentResult(token, orderResult.data.out_trade_no, points);

      wx.hideLoading();

      if (verifyResult.success && verifyResult.data.verified) {
        wx.showToast({
          title: 'Node.js支付成功',
          icon: 'success'
        });
        this.hideNodePaymentModal();
        // 刷新用户数据
        await this.fetchUserData(this.data.userId);
      } else {
        wx.showModal({
          title: '支付确认',
          content: 'Node.js支付已完成，但积分到账可能有延迟，请稍后查看',
          showCancel: false,
          confirmText: '知道了'
        });
      }

    } catch (error) {
      wx.hideLoading();

      if (error.errMsg === 'requestPayment:fail cancel') {
        wx.showToast({
          title: '支付已取消',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: error.message || 'Node.js支付失败',
          icon: 'none'
        });
        console.error('Node.js支付失败:', error);
      }
    } finally {
      // 重置支付处理状态
      this.setData({ isProcessingPayment: false });
    }
  },

  /**
   * 获取Node.js支付服务的token
   */
  async getNodePaymentToken() {
    try {
      // 获取微信登录code
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取微信登录code失败');
      }

      // 调用Node.js后端登录接口
      const response = await apiRequest('wechatLogin', {
        method: 'POST',
        data: {
          code: loginResult.code,
          userInfo: {
            nickName: this.data.userInfo.nickName,
            avatarUrl: this.data.userInfo.avatarUrl
          }
        }
      });

      if (response.success) {
        const token = response.data.token;
        this.setData({ nodePaymentToken: token });
        return token;
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('获取Node.js支付token失败:', error);
      return null;
    }
  },

  /**
   * 创建Node.js支付订单
   */
  async createNodePaymentOrder(token, amount, points) {
    try {
      const response = await authenticatedRequest('createPayment', token, {
        method: 'POST',
        data: {
          amount: amount,
          points: points,
          description: `Node.js积分充值${points}积分`
        }
      });

      return response;
    } catch (error) {
      console.error('创建Node.js支付订单失败:', error);
      throw error;
    }
  },

  /**
   * 验证Node.js支付结果
   */
  async verifyNodePaymentResult(token, orderNo, expectedPoints) {
    try {
      const response = await authenticatedRequest('verifyPayment', token, {
        method: 'POST',
        data: {
          orderNo: orderNo,
          expectedPoints: expectedPoints
        }
      });

      return response;
    } catch (error) {
      console.error('验证Node.js支付结果失败:', error);
      throw error;
    }
  },



});

