// 云函数：获取管理员待处理报价总数（聚合查询优化版）
// 仅针对管理员用户（adm == true）
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const $ = db.command.aggregate;

/**
 * 获取管理员所有待处理报价总数
 * 统计所有用户的 status 为 "pending" 的报价记录
 * 
 * @param {Object} event - 云函数事件参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 统计结果
 */
exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        code: -2,
        msg: '未获取到用户身份',
        totalPendingQuotes: 0,
        hasPendingQuotesCount: 0,
        hasPendingQuotes: false
      };
    }

    console.log('开始统计管理员待处理报价数量，openid:', openid);

    // 先查询用户信息，确认是管理员
    const userResult = await db.collection('users')
      .where({ _openid: openid })
      .field({ adm: true })
      .get();

    if (userResult.data.length === 0) {
      return {
        code: -3,
        msg: '用户信息不存在',
        totalPendingQuotes: 0,
        hasPendingQuotesCount: 0,
        hasPendingQuotes: false
      };
    }

    const user = userResult.data[0];
    
    // 如果不是管理员，返回空结果
    if (user.adm !== true) {
      return {
        code: 0,
        msg: '普通用户无权限查看管理员数据',
        totalPendingQuotes: 0,
        hasPendingQuotesCount: 0,
        hasPendingQuotes: false,
        isAdmin: false
      };
    }

    // 先查询所有报价总数，用于对比和调试
    const totalQuoteCount = await db.collection('quote_content')
      .count();

    console.log('所有报价总数量:', totalQuoteCount.total);

    // 使用聚合查询统计所有用户的待处理报价数量
    // 查询条件：status为pending
    const aggregateResult = await db.collection('quote_content')
      .aggregate()
      .match({
        status: 'pending'  // 只统计待处理状态的报价
      })
      .group({
        _id: null,
        totalPendingQuotes: $.sum(1), // 统计待处理报价数量
        hasPendingQuotesCount: $.sum(1) // 统计有待处理报价的记录数量（与上面相同）
      })
      .end();

    console.log('聚合查询结果:', {
      list: aggregateResult.list,
      total: aggregateResult.list.length,
      allQuotesTotal: totalQuoteCount.total
    });

    // 处理查询结果
    const result = aggregateResult.list[0] || { 
      totalPendingQuotes: 0, 
      hasPendingQuotesCount: 0 
    };

    const response = {
      code: 0,
      msg: '获取管理员待处理报价统计成功',
      totalPendingQuotes: result.totalPendingQuotes || 0,
      hasPendingQuotesCount: result.hasPendingQuotesCount || 0,
      hasPendingQuotes: (result.totalPendingQuotes || 0) > 0,
      isAdmin: true,
      // 调试信息
      debug: {
        allQuotesTotal: totalQuoteCount.total,
        aggregateResultCount: aggregateResult.list.length
      }
    };

    console.log('返回结果:', response);
    return response;

  } catch (error) {
    console.error('获取管理员待处理报价统计失败:', error);
    return {
      code: -1,
      msg: '获取管理员待处理报价统计失败: ' + error.message,
      totalPendingQuotes: 0,
      hasPendingQuotesCount: 0,
      hasPendingQuotes: false,
      error: error.message
    };
  }
};
