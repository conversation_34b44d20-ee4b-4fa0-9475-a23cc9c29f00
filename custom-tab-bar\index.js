Component({
  data: {
    active: 0,
    hidden: false, // 添加hidden属性控制是否隐藏tabBar
    list: [
      {
        pagePath: "/pages/home/<USER>",
        text: "主页",
        icon: "/assets/tabbar/home.png",
        selectedIcon: "/assets/tabbar/homes.png",
        color: "#666666",
        activeColor: "#2ba471"
      },
      {
        pagePath: "/pages/supply/supply",
        text: "供应",
        icon: "/assets/tabbar/box.png",
        selectedIcon: "/assets/tabbar/box_select.png",
        color: "#666666",
        activeColor: "#2ba471"
      },
      {
        pagePath: "/pages/demand/demand",
        text: "求购",
        icon: "/assets/tabbar/need.png",
        selectedIcon: "/assets/tabbar/need_select.png",
        color: "#666666",
        activeColor: "#2ba471"
      },
      {
        pagePath: "/pages/user/user",
        text: "我",
        icon: "/assets/tabbar/user.png",
        selectedIcon: "/assets/tabbar/user-select.png",
        color: "#666666",
        activeColor: "#2ba471"
      }
    ],
    // 需要显示tabBar但不在tabBar列表中的页面
    showTabBarPages: [
      "/pages/demand/demand",
      "/pages/user/my-supply/my-supply",
      "/pages/user/my-demand/my-demand"
    ],
    // 只在首页显示发布按钮
    showPublishButtonInHome: true,
    // 添加缓存，避免重复计算
    lastPagePath: "",
    lastTabIndex: -2,
    // 添加连接状态标志
    connectionState: "DISCONNECTED"
  },
  // 组件创建时自动更新选中状态
  lifetimes: {
    attached() {
      this.setData({
        active: this.getTabBarIndex()
      });
    }
  },
  // 页面显示时自动更新选中状态  
  
  pageLifetimes: {
    show() {
      // 获取当前页面路径
      const pages = getCurrentPages();
      if (!pages || pages.length === 0) return;
      
      const currentPage = pages[pages.length - 1];
      if (!currentPage || !currentPage.route) return;
      
      const currentPath = `/${currentPage.route}`;
      
      // 检查是否在首页，只有在首页才显示发布按钮
      const isHomePage = currentPath === "/pages/home/<USER>";
      console.log('当前页面路径:', currentPath, '是否是首页:', isHomePage);
      this.setData({
        showPublishButtonInHome: isHomePage
      });
      
      // 如果页面路径没有变化，且不是首次加载，则不需要重新计算tabbar索引
      if (currentPath === this.data.lastPagePath && this.data.lastTabIndex !== -2) {
      // 如果当前已经在第一个选项卡，并且active已经是0，则不需要再次设置
        if (this.data.lastTabIndex === 0 && this.data.active === 0) {
          return;
        }
        
        // 使用缓存的索引值
        if (this.data.active !== this.data.lastTabIndex) {
          this.setData({
            active: this.data.lastTabIndex
          });
        }
        return;
      }
      
      // 页面路径发生变化，重新计算tabbar索引
      const index = this.getTabBarIndex(currentPath);
      
      // 更新缓存
      this.data.lastPagePath = currentPath;
      this.data.lastTabIndex = index;
      
      // 如果索引没变，不需要更新视图
      if (index === this.data.active) return;
      
      this.setData({
        active: index
      });
    }
  },
  methods: {
    // 切换标签时触发
    onChange(e) {
      const index = e.detail.value;
      const targetPath = this.data.list[index].pagePath;

      // 获取当前页面路径
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentPath = currentPage ? `/${currentPage.route}` : '';

      // 检查是否点击的是当前页面的tab
      if (currentPath === targetPath) {
        // 如果是供应页面或求购页面，执行刷新操作
        if (targetPath === '/pages/supply/supply') {
          this.refreshSupplyPage(currentPage);
          return;
        } else if (targetPath === '/pages/demand/demand') {
          this.refreshDemandPage(currentPage);
          return;
        }
        // home和user页面不执行刷新，直接返回
        return;
      }

      this.setData({ active: index });

      try {
      // 使用switchTab跳转到对应的tabBar页面
      wx.switchTab({
          url: targetPath,
          fail: (err) => {
            console.error('跳转失败:', err);
          }
      });
      } catch (err) {
        console.error('switchTab调用异常:', err);
      }
    },
    
    // 发布按钮点击事件
    onPublishTap() {
      // 显示发布选项菜单
      wx.showActionSheet({
        itemList: ['发布供应', '发布求购'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 发布供应
            wx.navigateTo({
              url: '/pages/supply/publish/publish'
            });
          } else if (res.tapIndex === 1) {
            // 发布求购
            wx.navigateTo({
              url: '/pages/demand/publish/publish'
            });
          }
        }
      });
    },
    
    getTabBarIndex(pagePath) {
      // 如果提供了页面路径，直接使用；否则获取当前页面路径
      if (!pagePath) {
      const pages = getCurrentPages();
      // 添加保护逻辑：如果页面栈为空或当前页面不存在，返回默认值0
      if (!pages || pages.length === 0) {
        return 0;
      }
      
      const currentPage = pages[pages.length - 1];
      // 添加保护逻辑：如果当前页面或路由不存在，返回默认值0
      if (!currentPage || !currentPage.route) {
        return 0;
      }
      
        pagePath = `/${currentPage.route}`;
      }
      
      const list = this.data.list;
      
      // 检查当前页面是否在tabBar列表中
      for (let i = 0; i < list.length; i++) {
        if (list[i].pagePath === pagePath) {
          return i;
        }
      }
      
      // 如果当前页面不在tabBar列表中，但在需要显示tabBar的页面列表中
      // 则根据页面路径返回不同的索引
      if (this.data.showTabBarPages.includes(pagePath)) {
        // 用户相关页面应该激活"我"标签
        if (pagePath.startsWith("/pages/user/")) {
          return 3; // "我"标签的索引
        }
        return 0; // 其他页面默认激活首页标签
      }
      
      return -1; // 返回-1表示不显示tabBar
    },
    
    // 刷新供应页面
    refreshSupplyPage(currentPage) {
      if (!currentPage) {
        console.warn('当前页面对象不存在');
        return;
      }

      if (typeof currentPage.resetAllFiltersAndUI !== 'function') {
        console.warn('供应页面不支持刷新操作，缺少resetAllFiltersAndUI方法');
        // 显示提示给用户
        wx.showToast({
          title: '页面刷新功能暂不可用',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        // 重置所有筛选条件和UI状态
        currentPage.resetAllFiltersAndUI();

        // 回到顶部
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });

        // 显示刷新提示
        

        // 重新加载数据
        if (typeof currentPage.loadSupplyList === 'function') {
          currentPage.loadSupplyList();
        }
      } catch (error) {
        console.error('刷新供应页面失败:', error);
        wx.showToast({
          title: '刷新失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 刷新求购页面
    refreshDemandPage(currentPage) {
      if (!currentPage) {
        console.warn('当前页面对象不存在');
        return;
      }

      if (typeof currentPage.resetAllFiltersAndUI !== 'function') {
        console.warn('求购页面不支持刷新操作，缺少resetAllFiltersAndUI方法');
        // 显示提示给用户
        wx.showToast({
          title: '页面刷新功能暂不可用',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      try {
        // 重置所有筛选条件和UI状态
        currentPage.resetAllFiltersAndUI();

        // 回到顶部
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });

       

        // 重新加载数据
        if (typeof currentPage.loadDemandList === 'function') {
          currentPage.loadDemandList();
        }
      } catch (error) {
        console.error('刷新求购页面失败:', error);
        wx.showToast({
          title: '刷新失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 处理连接状态变化
    handleConnectionChange(newState) {
      // 只有在合法的状态转换下才更新状态
      const validTransitions = {
        "DISCONNECTED": ["CONNECTING"],
        "CONNECTING": ["CONNECTED", "DISCONNECTED"],
        "CONNECTED": ["DISCONNECTED"]
      };

      if (validTransitions[this.data.connectionState] &&
          validTransitions[this.data.connectionState].includes(newState)) {
        this.setData({
          connectionState: newState
        });
        return true;
      }

      console.warn(`无效的状态转换: ${this.data.connectionState} -> ${newState}`);
      return false;
    }
  }
}); 