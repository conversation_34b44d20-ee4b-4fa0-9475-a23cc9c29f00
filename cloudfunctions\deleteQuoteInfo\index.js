// 删除报价信息云函数
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID
  
  const { quoteId } = event

  console.log('删除报价请求:', {
    quoteId: quoteId,
    openId: openId
  })

  try {
    // 1. 验证管理员权限
    const isAdmin = await checkAdminPermission(openId)
    if (!isAdmin) {
      console.log('权限验证失败:', { openId, isAdmin })
      return {
        success: false,
        message: '无权限操作 - 只有管理员可以删除报价'
      }
    }

    console.log('权限验证通过:', { openId, isAdmin })
    
    // 2. 获取报价记录
    let quoteResult
    try {
      quoteResult = await db.collection('quote_content').doc(quoteId).get()
    } catch (error) {
      // 检查是否是文档不存在的错误
      if (error.errMsg && error.errMsg.includes('does not exist')) {
        return {
          success: false,
          message: '该报价已被删除',
          errorType: 'ALREADY_DELETED',
          needRefresh: true
        }
      }
      throw error // 其他错误继续抛出
    }

    if (!quoteResult.data) {
      return {
        success: false,
        message: '该报价已被删除',
        errorType: 'ALREADY_DELETED',
        needRefresh: true
      }
    }
    
    const quoteData = quoteResult.data
    const deleteResults = {
      quoteFiles: { success: false, message: '' },
      imageFiles: { success: false, message: '' },
      database: { success: false, message: '' }
    }
    
    // 3. 删除报价文件
    if (quoteData.fileID) {
      try {
        const fileDeleteResult = await deleteFileList([quoteData.fileID])
        deleteResults.quoteFiles = fileDeleteResult
      } catch (error) {
        console.error('删除报价文件失败:', error)
        deleteResults.quoteFiles = { success: false, message: `删除报价文件失败: ${error.message}` }
      }
    } else {
      deleteResults.quoteFiles = { success: true, message: '无报价文件需要删除' }
    }

    // 4. 删除图片文件
    if (quoteData.imageList && quoteData.imageList.length > 0) {
      try {
        const imageDeleteResult = await deleteFileList(quoteData.imageList)
        deleteResults.imageFiles = imageDeleteResult
      } catch (error) {
        console.error('删除图片文件失败:', error)
        deleteResults.imageFiles = { success: false, message: `删除图片文件失败: ${error.message}` }
      }
    } else {
      deleteResults.imageFiles = { success: true, message: '无图片文件需要删除' }
    }
    
    // 5. 删除数据库记录
    try {
      await db.collection('quote_content').doc(quoteId).remove()
      deleteResults.database = { success: true, message: '数据库记录删除成功' }
    } catch (error) {
      console.error('删除数据库记录失败:', error)
      deleteResults.database = { success: false, message: `删除数据库记录失败: ${error.message}` }
      
      // 数据库删除失败是致命错误
      return {
        success: false,
        message: '删除报价失败：数据库操作失败',
        details: deleteResults
      }
    }
    
    return {
      success: true,
      message: '报价删除成功',
      details: deleteResults
    }
    
  } catch (error) {
    console.error('删除报价失败:', error)
    return {
      success: false,
      message: '删除报价失败',
      error: error.message
    }
  }
}

/**
 * 检查管理员权限
 */
async function checkAdminPermission(openId) {
  try {
    // 尝试使用 openId 字段查询
    let userResult = await db.collection('users').where({
      openId: openId
    }).get()
    
    // 如果没有找到，尝试使用 _openid 字段查询
    if (userResult.data.length === 0) {
      userResult = await db.collection('users').where({
        _openid: openId
      }).get()
    }
    
    return userResult.data.length > 0 && userResult.data[0].adm === true
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

/**
 * 从文件路径提取文件夹路径
 */
function extractFolderPath(filePath, folderType) {
  try {
    // 示例路径: cloud://env.../quote_files/openid_quoteid/filename.ext
    // 提取: cloud://env.../quote_files/openid_quoteid/
    
    const pathParts = filePath.split('/')
    const folderIndex = pathParts.findIndex(part => part === folderType)
    
    if (folderIndex !== -1 && folderIndex + 1 < pathParts.length) {
      // 重构文件夹路径，包含到用户文件夹级别
      const folderPath = pathParts.slice(0, folderIndex + 2).join('/') + '/'
      return folderPath
    }
    
    return null
  } catch (error) {
    console.error('提取文件夹路径失败:', error)
    return null
  }
}

/**
 * 删除云存储文件夹及其所有内容
 */
async function deleteCloudFolder(folderPath) {
  try {
    console.log('尝试删除文件夹:', folderPath)

    // 微信云存储没有直接的文件夹删除功能
    // 我们需要通过其他方式来删除文件夹中的所有文件
    // 这里先记录日志，具体的文件删除在调用处进行

    return true
  } catch (error) {
    console.error('删除文件夹失败:', error)
    throw error
  }
}

/**
 * 删除具体的文件列表
 */
async function deleteFileList(fileList) {
  if (!fileList || fileList.length === 0) {
    return { success: true, message: '无文件需要删除' }
  }

  try {
    const deleteResult = await cloud.deleteFile({
      fileList: fileList
    })

    console.log('文件删除结果:', deleteResult)

    // 检查删除结果
    const failedFiles = deleteResult.fileList.filter(file => !file.status === 0)
    if (failedFiles.length > 0) {
      console.warn('部分文件删除失败:', failedFiles)
      return {
        success: false,
        message: `${failedFiles.length} 个文件删除失败`,
        failedFiles: failedFiles
      }
    }

    return {
      success: true,
      message: `成功删除 ${fileList.length} 个文件`
    }
  } catch (error) {
    console.error('删除文件列表失败:', error)
    throw error
  }
}
