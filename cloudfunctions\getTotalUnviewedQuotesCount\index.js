// 云函数：获取用户所有未查看的已完成报价总数（聚合查询优化版）
// 仅针对普通用户（adm == false）
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const $ = db.command.aggregate;

/**
 * 获取用户所有未查看的已完成报价总数
 * 使用聚合查询确保统计所有记录，解决分页限制问题
 * 
 * @param {Object} event - 云函数事件参数
 * @param {Object} context - 云函数上下文
 * @returns {Object} 统计结果
 */
exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;

    if (!openid) {
      return {
        code: -2,
        msg: '未获取到用户身份',
        totalUnviewedQuotes: 0,
        hasUnviewedQuotesCount: 0,
        hasUnviewedQuotes: false
      };
    }

    console.log('开始统计用户未查看的已完成报价数量，openid:', openid);

    // 先查询用户信息，确认是普通用户
    const userResult = await db.collection('users')
      .where({ _openid: openid })
      .field({ adm: true })
      .get();

    if (userResult.data.length === 0) {
      return {
        code: -3,
        msg: '用户信息不存在',
        totalUnviewedQuotes: 0,
        hasUnviewedQuotesCount: 0,
        hasUnviewedQuotes: false
      };
    }

    const user = userResult.data[0];
    
    // 如果是管理员，不显示红点
    if (user.adm === true) {
      return {
        code: 0,
        msg: '管理员用户不显示红点',
        totalUnviewedQuotes: 0,
        hasUnviewedQuotesCount: 0,
        hasUnviewedQuotes: false,
        isAdmin: true
      };
    }

    // 先查询用户总的报价数量，用于对比和调试
    const totalQuoteCount = await db.collection('quote_content')
      .where({ openId: openid })
      .count();

    console.log('用户总报价数量:', totalQuoteCount.total);

    // 使用聚合查询统计所有未查看的已完成报价数量
    // 查询条件：status为completed且isViewed不为true
    const aggregateResult = await db.collection('quote_content')
      .aggregate()
      .match({
        openId: openid,
        status: 'completed',
        $or: [
          { isViewed: { $exists: false } }, // isViewed字段不存在
          { isViewed: { $ne: true } }       // isViewed不为true
        ]
      })
      .group({
        _id: null,
        totalUnviewedQuotes: $.sum(1), // 统计未查看的已完成报价数量
        hasUnviewedQuotesCount: $.sum(1) // 统计有未查看报价的记录数量（与上面相同）
      })
      .end();

    console.log('聚合查询结果:', {
      list: aggregateResult.list,
      total: aggregateResult.list.length,
      userTotalQuotes: totalQuoteCount.total
    });

    // 处理查询结果
    const result = aggregateResult.list[0] || { 
      totalUnviewedQuotes: 0, 
      hasUnviewedQuotesCount: 0 
    };

    const response = {
      code: 0,
      msg: '获取未查看报价统计成功',
      totalUnviewedQuotes: result.totalUnviewedQuotes || 0,
      hasUnviewedQuotesCount: result.hasUnviewedQuotesCount || 0,
      hasUnviewedQuotes: (result.totalUnviewedQuotes || 0) > 0,
      isAdmin: false,
      // 调试信息
      debug: {
        userTotalQuotes: totalQuoteCount.total,
        aggregateResultCount: aggregateResult.list.length
      }
    };

    console.log('返回结果:', response);
    return response;

  } catch (error) {
    console.error('获取未查看报价统计失败:', error);
    return {
      code: -1,
      msg: '获取未查看报价统计失败: ' + error.message,
      totalUnviewedQuotes: 0,
      hasUnviewedQuotesCount: 0,
      hasUnviewedQuotes: false,
      error: error.message
    };
  }
};
