// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 更新植物关键字命中次数
 * @param {Object} event
 * @param {string} event.plantName - 植物名称
 * @param {string} event.openid - 用户openid（可选，用于防重复点击）
 * @param {string} event.source - 来源标记（可选）
 * @param {Object} context
 */
exports.main = async (event, context) => {
  const { plantName, openid, source = 'supply_search' } = event
  
  // 参数验证
  if (!plantName || typeof plantName !== 'string') {
    return {
      success: false,
      message: '植物名称不能为空'
    }
  }
  
  try {
    // 直接查找并更新plants_list中的植物记录
    const plantResult = await db.collection('plants_list')
      .where({ name: plantName })
      .limit(1)
      .get()

    if (plantResult.data.length === 0) {
      return {
        success: false,
        message: '植物不存在'
      }
    }

    const plant = plantResult.data[0]

    // 更新命中次数
    const updateResult = await db.collection('plants_list')
      .doc(plant._id)
      .update({
        data: {
          hit: db.command.inc(1) // 原子性增加1
        }
      })

    return {
      success: true,
      message: '命中次数更新成功',
      data: {
        plantName: plantName,
        newHitCount: (plant.hit || 0) + 1
      }
    }
    
  } catch (error) {
    console.error('更新植物命中次数失败:', error)
    return {
      success: false,
      message: '更新失败',
      error: error.message
    }
  }
}
