// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID
  
  // 从请求中获取参数
  const { page = 1, pageSize = 10, status } = event
  
  try {
    // 构建查询条件
    const condition = {}
    
    // 如果指定了状态，则按状态筛选
    if (status) {
      condition.status = status
    }
    
    // 计算总数
    const countResult = await db.collection('quote_content')
      .where(condition)
      .count()
    const total = countResult.total
    
    // 查询数据 - 管理员使用聚合查询实现红点优先排序
    const result = await db.collection('quote_content')
      .aggregate()
      .match(condition)
      .addFields({
        // 管理员排序权重：pending状态的报价权重最高
        adminSortWeight: {
          $cond: {
            if: { $eq: ['$status', 'pending'] },
            then: 1,  // 待处理的报价，权重为1（最高优先级）
            else: 0   // 其他报价，权重为0
          }
        }
      })
      .sort({
        adminSortWeight: -1,  // 首先按管理员权重降序排列（pending在前）
        createTime: -1        // 然后按创建时间降序排列
      })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .end()
    
    // 处理数据，添加状态文本和管理员红点逻辑
    const quoteList = result.list.map(item => {
      // 状态文本处理
      let statusText = '待处理'
      let statusClass = 'status-pending'
      
      if (item.status === 'completed') {
        statusText = '已完成'
        statusClass = 'status-completed'
      } else if (item.response) {
        statusText = '已回复'
        statusClass = 'status-responded'
      }
      
      return {
        ...item,
        statusText,
        statusClass,
        // 格式化日期
        createTimeStr: formatDate(item.createTime),
        // 如果有文件上传时间，也格式化
        fileUploadTimeStr: item.fileUploadTime ? formatDate(item.fileUploadTime) : '',
        // 管理员红点显示逻辑：pending状态显示红点
        showRedDot: item.status === 'pending',
        // 移除临时的排序权重字段
        adminSortWeight: undefined
      }
    })
    
    return {
      success: true,
      data: quoteList,
      total,
      pageSize,
      currentPage: page,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (err) {
    console.error('获取报价列表失败:', err)
    return {
      success: false,
      errMsg: err.message || '获取报价列表失败'
    }
  }
}

// 格式化日期函数
function formatDate(dateObj) {
  if (!dateObj) return ''
  
  // 将日期字符串转换为Date对象
  const date = new Date(dateObj)
  
  // 获取年月日
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  // 获取时分
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  // 返回格式化的日期字符串
  return `${year}-${month}-${day} ${hours}:${minutes}`
} 