/**
 * 用户注册逻辑改进测试文件
 * 用于验证改进后的功能是否正常工作
 */

// 测试昵称生成策略
function testNicknameGeneration() {
  console.log('=== 测试昵称生成策略 ===');
  
  // 模拟时间戳+随机数生成
  const generateRandomNickname = () => {
    // 使用时间戳(6位) + 随机数(1位) = 7位数字
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 10);
    return `用户${timestamp}${random}`;
  };
  
  // 生成10个测试昵称
  for (let i = 0; i < 10; i++) {
    const nickname = generateRandomNickname();
    console.log(`生成的昵称 ${i + 1}: ${nickname}`);
  }
  
  console.log('昵称长度验证:', `用户${Date.now().toString().slice(-6)}0`.length);
}

// 测试指数退避重试策略
function testExponentialBackoff() {
  console.log('=== 测试指数退避重试策略 ===');
  
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
  
  const retryWithExponentialBackoff = async (fn, maxRetries = 3, context = 'test') => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        console.log(`${context} 第${attempt + 1}次尝试...`);
        return await fn();
      } catch (error) {
        lastError = error;
        console.error(`${context} 第${attempt + 1}次尝试失败:`, error.message);
        
        if (attempt < maxRetries) {
          // 指数退避：100ms, 200ms, 400ms, 800ms...
          const delayTime = Math.pow(2, attempt) * 100;
          console.log(`${context} 等待${delayTime}ms后重试...`);
          await delay(delayTime);
        }
      }
    }
    
    throw new Error(`${context} 重试${maxRetries + 1}次后仍然失败: ${lastError.message}`);
  };
  
  // 模拟失败的函数
  const mockFailingFunction = () => {
    return new Promise((resolve, reject) => {
      // 70% 概率失败
      if (Math.random() < 0.7) {
        reject(new Error('模拟网络错误'));
      } else {
        resolve('成功');
      }
    });
  };
  
  // 测试重试机制
  retryWithExponentialBackoff(mockFailingFunction, 3, 'mockFunction')
    .then(result => {
      console.log('最终结果:', result);
    })
    .catch(error => {
      console.error('最终失败:', error.message);
    });
}

// 测试网络状态检测
function testNetworkDetection() {
  console.log('=== 测试网络状态检测 ===');
  
  // 模拟微信API
  const mockWx = {
    getNetworkType: (options) => {
      // 模拟不同的网络状态
      const networkTypes = ['wifi', '4g', '3g', '2g', 'none'];
      const randomType = networkTypes[Math.floor(Math.random() * networkTypes.length)];
      
      setTimeout(() => {
        if (Math.random() < 0.9) { // 90% 成功率
          options.success({ networkType: randomType });
        } else {
          options.fail(new Error('获取网络状态失败'));
        }
      }, 100);
    }
  };
  
  const checkNetworkStatus = () => {
    return new Promise((resolve) => {
      mockWx.getNetworkType({
        success: (res) => {
          const networkType = res.networkType;
          if (networkType === 'none') {
            resolve({ connected: false, type: 'none' });
          } else {
            resolve({ connected: true, type: networkType });
          }
        },
        fail: () => {
          // 如果获取网络状态失败，假设网络可用
          resolve({ connected: true, type: 'unknown' });
        }
      });
    });
  };
  
  // 测试网络检测
  checkNetworkStatus().then(status => {
    console.log('网络状态:', status);
    if (!status.connected) {
      console.log('网络不可用，应该显示错误提示');
    } else {
      console.log(`网络可用，类型: ${status.type}`);
    }
  });
}

// 测试位置获取降级策略
function testLocationFallback() {
  console.log('=== 测试位置获取降级策略 ===');
  
  // 模拟mapUtils
  const mockMapUtils = {
    getCurrentLocationAndReverse: (options) => {
      setTimeout(() => {
        // 50% 概率成功
        if (Math.random() < 0.5) {
          options.success({
            status: 0,
            result: {
              location: { lat: 39.9042, lng: 116.4074 },
              address_component: {
                province: '北京市',
                city: '北京市'
              }
            }
          });
        } else {
          options.fail(new Error('逆地址解析失败'));
        }
      }, 200);
    }
  };
  
  // 模拟wx.getLocation
  const mockGetLocation = (options) => {
    setTimeout(() => {
      // 80% 概率成功
      if (Math.random() < 0.8) {
        options.success({
          latitude: 39.9042,
          longitude: 116.4074
        });
      } else {
        options.fail(new Error('基础定位失败'));
      }
    }, 300);
  };
  
  const getLocationWithFallback = () => {
    return new Promise((resolve) => {
      // 首先尝试高精度位置获取
      mockMapUtils.getCurrentLocationAndReverse({
        success: (result) => {
          if (result.status === 0) {
            const addressComponent = result.result.address_component;
            resolve({
              province: addressComponent.province,
              city: addressComponent.city,
              latitude: result.result.location.lat,
              longitude: result.result.location.lng,
              method: 'reverse_geocoding'
            });
          } else {
            console.log('逆地址解析失败，尝试普通定位...');
            fallbackToBasicLocation(resolve);
          }
        },
        fail: (err) => {
          console.log('高精度定位失败，尝试普通定位...', err.message);
          fallbackToBasicLocation(resolve);
        }
      });
    });
  };
  
  const fallbackToBasicLocation = (resolve) => {
    mockGetLocation({
      success: (res) => {
        console.log('基础定位成功');
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          method: 'basic_location'
        });
      },
      fail: (err) => {
        console.log('基础定位也失败，使用默认位置策略:', err.message);
        resolve(null);
      }
    });
  };
  
  // 测试位置获取
  getLocationWithFallback().then(locationInfo => {
    if (locationInfo) {
      console.log('位置获取成功:', locationInfo);
    } else {
      console.log('位置获取失败，将创建无位置信息的用户');
    }
  });
}

// 运行所有测试
function runAllTests() {
  console.log('开始运行用户注册逻辑改进测试...\n');
  
  testNicknameGeneration();
  console.log('\n');
  
  testExponentialBackoff();
  console.log('\n');
  
  testNetworkDetection();
  console.log('\n');
  
  testLocationFallback();
  console.log('\n');
  
  console.log('所有测试已启动，请查看控制台输出结果');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testNicknameGeneration,
    testExponentialBackoff,
    testNetworkDetection,
    testLocationFallback,
    runAllTests
  };
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  window.userImprovementsTest = {
    testNicknameGeneration,
    testExponentialBackoff,
    testNetworkDetection,
    testLocationFallback,
    runAllTests
  };
}
