// 云函数 - quoteFileManager
// 报价文件管理云函数：上传、删除、获取详情
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID

  const { action, quoteId, fileData, fileName } = event



  try {
    switch (action) {
      case 'upload':
        return await uploadQuoteFile(quoteId, fileData, fileName, openId)
      case 'delete':
        return await deleteQuoteFile(quoteId, openId)
      case 'getDetail':
        return await getQuoteDetail(quoteId, openId)
      default:
        return {
          success: false,
          message: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('报价文件管理失败:', error)
    return {
      success: false,
      message: '操作失败',
      error: error.message,
      stack: error.stack
    }
  }
}

/**
 * 上传报价文件
 */
async function uploadQuoteFile(quoteId, fileData, fileName, openId) {
  let fileID = null // 声明在函数顶部，以便在错误处理中使用

  try {
    // 1. 首先获取报价数据，验证权限
    const quoteResult = await db.collection('quote_content').doc(quoteId).get()
    if (!quoteResult.data) {
      return {
        success: false,
        message: '报价记录不存在'
      }
    }
    
    const quoteData = quoteResult.data
    
    // 2. 检查权限：只有管理员可以上传文件
    const userResult = await db.collection('users').where({
      openId: openId
    }).get()

    // 如果没有找到用户记录，尝试使用 _openid 字段查询
    let alternativeUserResult = null
    if (userResult.data.length === 0) {
      alternativeUserResult = await db.collection('users').where({
        _openid: openId
      }).get()
    }

    // 使用找到的用户记录
    const finalUserResult = userResult.data.length > 0 ? userResult : alternativeUserResult
    const isAdmin = finalUserResult && finalUserResult.data.length > 0 && finalUserResult.data[0].adm === true

    if (!isAdmin) {
      return {
        success: false,
        message: '无权限操作 - 只有管理员可以上传报价文件'
      }
    }
    
    // 3. 保留原有的文件命名逻辑
    const folderPath = `quote_files/${quoteData.openId}_${quoteId}`
    
    // 获取项目名称作为文件名
    const projectName = quoteData.projectName || '报价文件'
    
    // 获取文件扩展名
    const extension = fileName.substring(fileName.lastIndexOf('.'))
    
    // 去除项目名称中的特殊字符，避免文件路径出错
    const safeProjectName = projectName.replace(/[\\/:*?"<>|]/g, '_')
    const cloudPath = `${folderPath}/${safeProjectName}${extension}`
    
    // 4. 清理旧文件（如果存在）
    if (quoteData.fileID) {
      try {
        await cloud.deleteFile({
          fileList: [quoteData.fileID]
        })
        console.log('已删除旧文件:', quoteData.fileID)
      } catch (deleteError) {
        console.warn('删除旧文件失败:', deleteError)
        // 继续执行，不影响新文件上传
      }
    }
    
    // 5. 上传新文件
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: Buffer.from(fileData, 'base64')
    })

    if (!uploadResult.fileID) {
      throw new Error('文件上传失败：未获得文件ID')
    }

    fileID = uploadResult.fileID
    
    // 6. 更新数据库记录
    const updateData = {
      fileID: fileID,
      fileUploadTime: new Date(),
      status: 'completed',
      response: true,
      updateTime: new Date()
    }

    const updateResult = await db.collection('quote_content').doc(quoteId).update({
      data: updateData
    })

    if (updateResult.stats.updated === 0) {
      throw new Error('数据库更新失败：没有记录被更新')
    }
    
    // 7. 返回更新后的完整数据
    const updatedResult = await db.collection('quote_content').doc(quoteId).get()
    const updatedData = updatedResult.data
    
    // 格式化时间
    if (updatedData.createTime) {
      const createTime = new Date(updatedData.createTime)
      updatedData.createTimeFormatted = formatDateTime(createTime)
    }
    
    if (updatedData.fileUploadTime) {
      const uploadTime = new Date(updatedData.fileUploadTime)
      updatedData.fileUploadTimeFormatted = formatDateTime(uploadTime)
    }
    
    return {
      success: true,
      message: '文件上传成功',
      data: updatedData,
      fileID: fileID
    }
    
  } catch (error) {
    console.error('上传文件失败:', error)

    // 如果文件已经上传但数据库更新失败，尝试清理文件
    if (error.message.includes('数据库更新失败') && fileID) {
      try {
        await cloud.deleteFile({
          fileList: [fileID]
        })
        console.log('已清理失败的上传文件:', fileID)
      } catch (cleanupError) {
        console.error('清理文件失败:', cleanupError)
      }
    }

    return {
      success: false,
      message: `上传失败: ${error.message}`,
      error: error.message,
      stack: error.stack
    }
  }
}

/**
 * 删除报价文件
 */
async function deleteQuoteFile(quoteId, openId) {
  try {
    // 1. 获取报价数据
    const quoteResult = await db.collection('quote_content').doc(quoteId).get()
    if (!quoteResult.data) {
      return {
        success: false,
        message: '报价记录不存在'
      }
    }
    
    const quoteData = quoteResult.data
    
    // 2. 检查权限：只有管理员可以删除文件
    const userResult = await db.collection('users').where({
      openId: openId
    }).get()

    // 如果没有找到用户记录，尝试使用 _openid 字段查询
    let alternativeUserResult = null
    if (userResult.data.length === 0) {
      alternativeUserResult = await db.collection('users').where({
        _openid: openId
      }).get()
    }

    // 使用找到的用户记录
    const finalUserResult = userResult.data.length > 0 ? userResult : alternativeUserResult
    const isAdmin = finalUserResult && finalUserResult.data.length > 0 && finalUserResult.data[0].adm === true

    if (!isAdmin) {
      return {
        success: false,
        message: '无权限操作 - 只有管理员可以删除报价文件'
      }
    }
    
    // 3. 删除云存储文件
    if (quoteData.fileID) {
      try {
        await cloud.deleteFile({
          fileList: [quoteData.fileID]
        })
      } catch (deleteError) {
        console.warn('删除云存储文件失败:', deleteError)
        // 继续执行数据库更新
      }
    }
    
    // 4. 更新数据库记录
    const updateData = {
      fileID: '',
      fileUploadTime: null,
      response: false,
      status: 'pending',
      updateTime: new Date()
    }
    
    await db.collection('quote_content').doc(quoteId).update({
      data: updateData
    })
    
    // 5. 返回更新后的数据
    const updatedResult = await db.collection('quote_content').doc(quoteId).get()
    const updatedData = updatedResult.data
    
    // 格式化时间
    if (updatedData.createTime) {
      const createTime = new Date(updatedData.createTime)
      updatedData.createTimeFormatted = formatDateTime(createTime)
    }
    
    return {
      success: true,
      message: '文件删除成功',
      data: updatedData
    }
    
  } catch (error) {
    console.error('删除文件失败:', error)
    return {
      success: false,
      message: '删除失败',
      error: error.message
    }
  }
}

/**
 * 获取报价详情（解决权限问题）
 */
async function getQuoteDetail(quoteId, openId) {
  try {
    // 直接从数据库获取数据，云函数有完整权限
    const result = await db.collection('quote_content').doc(quoteId).get()
    
    if (!result.data) {
      return {
        success: false,
        message: '报价记录不存在'
      }
    }
    
    const quoteData = result.data
    
    // 格式化时间
    if (quoteData.createTime) {
      const createTime = new Date(quoteData.createTime)
      quoteData.createTimeFormatted = formatDateTime(createTime)
    }
    
    if (quoteData.fileUploadTime) {
      const uploadTime = new Date(quoteData.fileUploadTime)
      quoteData.fileUploadTimeFormatted = formatDateTime(uploadTime)
    }
    
    return {
      success: true,
      data: quoteData
    }
    
  } catch (error) {
    console.error('获取报价详情失败:', error)
    return {
      success: false,
      message: '获取数据失败',
      error: error.message
    }
  }
}



/**
 * 格式化日期时间
 */
function formatDateTime(date) {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
