// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID
  
  // 从请求中获取参数
  const { userId, page = 1, pageSize = 10 } = event

  // 获取当前用户信息，判断是否为管理员
  let isAdmin = false;
  try {
    const userResult = await db.collection('users')
      .where({ _openid: openId })
      .field({ adm: true })
      .get();

    if (userResult.data.length > 0) {
      isAdmin = userResult.data[0].adm === true;
    }
  } catch (error) {
    console.log('获取用户信息失败，默认为普通用户:', error);
  }



  // 构建查询条件
  const condition = {}
  
  // 如果提供了userId，则按userId查询，否则按openId查询
  if (userId) {
    condition.userId = userId
  } else {
    condition.openId = openId
  }
  
  try {
    // 计算总数
    const countResult = await db.collection('quote_content')
      .where(condition)
      .count()
    const total = countResult.total
    
    // 查询数据 - 根据用户类型使用不同的排序逻辑
    let result;

    if (isAdmin) {
      // 管理员：pending状态的报价优先排序
      result = await db.collection('quote_content')
        .aggregate()
        .match(condition)
        .addFields({
          // 管理员排序权重：pending状态的报价权重最高
          adminSortWeight: {
            $cond: {
              if: { $eq: ['$status', 'pending'] },
              then: 1,  // 待处理的报价，权重为1（最高优先级）
              else: 0   // 其他报价，权重为0
            }
          }
        })
        .sort({
          adminSortWeight: -1,  // 首先按管理员权重降序排列（pending在前）
          createTime: -1        // 然后按创建时间降序排列
        })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .end();
    } else {
      // 普通用户：已完成且未查看的报价优先排序
      result = await db.collection('quote_content')
        .aggregate()
        .match(condition)
        .addFields({
          // 普通用户排序权重：已完成且未查看的报价权重最高
          userSortWeight: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$status', 'completed'] },
                  {
                    $or: [
                      { $eq: [{ $type: '$isViewed' }, 'missing'] }, // isViewed字段不存在
                      { $ne: ['$isViewed', true] }                  // isViewed不为true
                    ]
                  }
                ]
              },
              then: 1,  // 有红点的报价，权重为1（最高优先级）
              else: 0   // 其他报价，权重为0
            }
          }
        })
        .sort({
          userSortWeight: -1,   // 首先按用户权重降序排列（有红点的在前）
          createTime: -1        // 然后按创建时间降序排列
        })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .end();
    }
    
    // 处理数据，添加状态文本和红点显示逻辑
    const quoteList = result.list.map(item => {
      let statusText = '待处理'
      let statusClass = 'status-pending'
      
      if (item.status === 'completed') {
        statusText = '平台已完成报价，请进入查看'
        statusClass = 'status-completed'
      } else if (item.response) {
        statusText = '已回复'
        statusClass = 'status-responded'
      }
      
      return {
        ...item,
        statusText,
        statusClass,
        // 格式化日期
        createTimeStr: formatDate(item.createTime),
        // 如果有文件上传时间，也格式化
        fileUploadTimeStr: item.fileUploadTime ? formatDate(item.fileUploadTime) : '',
        // 根据用户类型添加不同的红点显示逻辑
        showRedDot: isAdmin
          ? item.status === 'pending'  // 管理员：pending状态显示红点
          : (item.status === 'completed' && !item.isViewed), // 普通用户：已完成且未查看显示红点
        // 移除临时的排序权重字段
        adminSortWeight: undefined,
        userSortWeight: undefined
      }
    })
    
    return {
      success: true,
      data: quoteList,
      total,
      pageSize,
      currentPage: page,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (err) {
    console.error('获取报价列表失败:', err)
    return {
      success: false,
      errMsg: err.message || '获取报价列表失败'
    }
  }
}

// 格式化日期函数
function formatDate(dateObj) {
  if (!dateObj) return ''
  
  // 将日期字符串转换为Date对象
  const date = new Date(dateObj)
  
  // 获取年月日
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  // 获取时分
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  // 返回格式化的日期字符串
  return `${year}-${month}-${day} ${hours}:${minutes}`
}